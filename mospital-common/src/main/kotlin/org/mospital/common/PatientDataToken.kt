package org.mospital.common

import com.fasterxml.jackson.module.kotlin.jacksonTypeRef

/**
 * 患者数据Token
 *
 * @deprecated 此类已废弃，建议直接使用 TokenManager 进行token操作。
 *
 * **迁移指南：**
 *
 * ```kotlin
 * // 旧用法（已废弃）
 * val token = PatientDataToken("data123", "patient456")
 * val tokenString = token.generateToken()
 * val parsed = PatientDataToken.parse(tokenString)
 *
 * // 新用法（推荐）
 * val tokenManager = TokenManager()
 * val data = PatientDataToken("data123", "patient456")
 * val tokenString = tokenManager.generateToken(data)
 * val parsed = tokenManager.parseToken(tokenString, jacksonTypeRef<PatientDataToken>())
 *
 * // 或者使用静态方法（向后兼容）
 * val tokenString = TokenManager.generateToken(data)
 * val parsed = TokenManager.parseToken(tokenString, jacksonTypeRef<PatientDataToken>())
 * ```
 *
 * **使用TokenManager的优势：**
 * - 支持任意数据类型，不限于PatientDataToken
 * - 支持自定义缓存配置（TTL、内存限制等）
 * - 支持缓存监控和统计
 * - 更好的Spring集成支持
 * - 更灵活的缓存策略选择
 *
 * @param dataId 数据ID
 * @param patientId 患者ID
 * @since 1.0.0
 */
@Deprecated(
    message = "PatientDataToken已废弃，请直接使用TokenManager进行token操作",
    replaceWith = ReplaceWith(
        "TokenManager.generateToken(data)",
        "org.mospital.common.TokenManager"
    ),
    level = DeprecationLevel.WARNING
)
data class PatientDataToken(
    val dataId: String,
    val patientId: String
) {
    companion object {
        /**
         * 解析token为PatientDataToken
         *
         * @deprecated 请使用 TokenManager.parseToken(token, jacksonTypeRef<PatientDataToken>())
         */
        @Deprecated(
            message = "请使用 TokenManager.parseToken(token, jacksonTypeRef<PatientDataToken>())",
            replaceWith = ReplaceWith(
                "TokenManager.parseToken(token, jacksonTypeRef<PatientDataToken>())",
                "org.mospital.common.TokenManager",
                "com.fasterxml.jackson.module.kotlin.jacksonTypeRef"
            ),
            level = DeprecationLevel.WARNING
        )
        @JvmStatic
        fun parse(token: String): PatientDataToken? = TokenManager.parseToken(token, jacksonTypeRef<PatientDataToken>())
    }

    /**
     * 生成token字符串
     *
     * @deprecated 请使用 TokenManager.generateToken(data)
     */
    @Deprecated(
        message = "请使用 TokenManager.generateToken(data)",
        replaceWith = ReplaceWith(
            "TokenManager.generateToken(this)",
            "org.mospital.common.TokenManager"
        ),
        level = DeprecationLevel.WARNING
    )
    fun generateToken(): String = TokenManager.generateToken(this)

}