package org.mospital.common

import com.fasterxml.jackson.core.type.TypeReference
import com.github.benmanes.caffeine.cache.Cache
import com.github.benmanes.caffeine.cache.Caffeine
import com.github.benmanes.caffeine.cache.Weigher
import org.mospital.jackson.JacksonKit
import org.slf4j.LoggerFactory
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * Token管理器，用于生成和解析固定长度的token
 *
 * @param cache 缓存实例，用于存储token与数据的映射关系
 * @param enableMonitoring 是否启用缓存监控，启用后会在生成token时输出缓存统计日志
 */
class TokenManager(
    private val cache: Cache<String, String>,
    private val enableMonitoring: Boolean = false
) {

    /**
     * 使用默认缓存配置的构造器
     * 默认策略：30分钟TTL + 基于内存权重限制（10MB）
     */
    constructor() : this(createDefaultCache(), false)

    /**
     * 使用默认缓存配置但启用监控的构造器
     */
    constructor(enableMonitoring: Boolean) : this(createDefaultCache(), enableMonitoring)

    companion object {
        private val logger = LoggerFactory.getLogger(TokenManager::class.java)

        /**
         * 统一的权重计算器 - 按 UTF-8 字节数计算
         * 避免重复代码，保持一致性
         */
        private val STRING_WEIGHER: Weigher<String, String> = Weigher { key, value ->
            key.toByteArray(Charsets.UTF_8).size + value.toByteArray(Charsets.UTF_8).size
        }

        /**
         * 创建默认缓存配置
         * 使用内存权重而不是固定条目数，更加灵活
         */
        private fun createDefaultCache(): Cache<String, String> {
            return Caffeine.newBuilder()
                .expireAfterWrite(30, TimeUnit.MINUTES)
                .maximumWeight(10 * 1024 * 1024) // 10MB内存限制
                .weigher(STRING_WEIGHER)
                .recordStats() // 启用统计，便于监控
                .build()
        }

        /**
         * 创建TTL-Only TokenManager（无大小限制）
         * 适用于低并发、短期token场景
         */
        @JvmStatic
        fun createTTLOnly(ttl: Long, unit: TimeUnit, enableMonitoring: Boolean = false): TokenManager {
            val cache = Caffeine.newBuilder()
                .expireAfterWrite(ttl, unit)
                .recordStats()
                .build<String, String>()
            return TokenManager(cache, enableMonitoring)
        }

        /**
         * 创建Weight-Based TokenManager（精确内存控制）
         * 适用于需要精确控制内存使用的场景
         */
        @JvmStatic
        fun createWeightBased(
            ttl: Long,
            unit: TimeUnit,
            maxWeightBytes: Long,
            enableMonitoring: Boolean = false
        ): TokenManager {
            val cache = Caffeine.newBuilder()
                .expireAfterWrite(ttl, unit)
                .maximumWeight(maxWeightBytes)
                .weigher(STRING_WEIGHER) // 使用统一的权重计算器
                .recordStats()
                .build<String, String>()
            return TokenManager(cache, enableMonitoring)
        }

        /**
         * 创建Adaptive TokenManager（基于JVM内存压力）
         * 适用于内存敏感的环境
         */
        @JvmStatic
        fun createAdaptive(ttl: Long, unit: TimeUnit, enableMonitoring: Boolean = false): TokenManager {
            val cache = Caffeine.newBuilder()
                .expireAfterWrite(ttl, unit)
                .softValues() // 允许GC在内存压力下回收
                .recordStats()
                .build<String, String>()
            return TokenManager(cache, enableMonitoring)
        }

        /**
         * 默认的TokenManager实例，使用默认缓存配置
         * 为了保持向后兼容性而提供
         */
        @JvmStatic
        private val defaultInstance: TokenManager = TokenManager()

        /**
         * 静态方法：生成token（使用默认实例）
         * 为了保持向后兼容性而提供
         */
        @JvmStatic
        @JvmName("generateTokenStatic")
        fun generateToken(data: Any): String = defaultInstance.generateToken(data)

        /**
         * 静态方法：解析token（使用默认实例）
         * 为了保持向后兼容性而提供
         */
        @JvmStatic
        @JvmName("parseTokenStatic")
        fun <T> parseToken(token: String, valueType: TypeReference<T>): T? =
            defaultInstance.parseToken(token, valueType)
    }

    /**
     * 生成固定长度的token（32字符）
     * @param data 要编码的数据
     * @return 32字符的UUID token
     */
    fun generateToken(data: Any): String {
        val jsonText = JacksonKit.writeValueAsString(data)
        val tokenKey = UUID.randomUUID().toString().replace("-", "")

        cache.put(tokenKey, jsonText)

        // 如果启用监控，输出缓存统计信息
        if (enableMonitoring) {
            logCacheStats("after generateToken")
        }

        return tokenKey
    }

    /**
     * 解析token获取原始数据
     * @param token 要解析的token
     * @param valueType 目标类型
     * @return 解析后的数据，如果token不存在或解析失败则返回null
     */
    fun <T> parseToken(token: String, valueType: TypeReference<T>): T? {
        return try {
            val jsonText = cache.getIfPresent(token) ?: return null
            JacksonKit.readValue(jsonText, valueType)
        } catch (_: Exception) {
            null
        }
    }

    /**
     * 缓存统计信息数据类
     */
    data class CacheStats(
        val size: Long,
        val hitRate: Double,
        val hitCount: Long,
        val missCount: Long,
        val evictionCount: Long,
        val averageLoadTimeMs: Double,
        val monitoringEnabled: Boolean,
        val error: String? = null
    )

    /**
     * 获取缓存统计信息
     * @return 类型安全的缓存统计信息对象
     */
    fun getCacheStats(): CacheStats {
        val estimatedSize = cache.estimatedSize()
        return try {
            val stats = cache.stats()
            CacheStats(
                size = estimatedSize,
                hitRate = stats.hitRate() * 100,
                hitCount = stats.hitCount(),
                missCount = stats.missCount(),
                evictionCount = stats.evictionCount(),
                averageLoadTimeMs = stats.averageLoadPenalty() / 1_000_000.0,
                monitoringEnabled = enableMonitoring
            )
        } catch (_: IllegalStateException) {
            CacheStats(
                size = estimatedSize,
                hitRate = 0.0,
                hitCount = 0,
                missCount = 0,
                evictionCount = 0,
                averageLoadTimeMs = 0.0,
                monitoringEnabled = enableMonitoring,
                error = "Cache stats not available. Ensure cache is built with .recordStats()."
            )
        } catch (e: Exception) {
            CacheStats(
                size = estimatedSize,
                hitRate = 0.0,
                hitCount = 0,
                missCount = 0,
                evictionCount = 0,
                averageLoadTimeMs = 0.0,
                monitoringEnabled = enableMonitoring,
                error = e.message ?: "Unknown error while retrieving stats"
            )
        }
    }

    /**
     * 输出缓存统计信息到日志
     */
    fun logCacheStats() {
        logCacheStats("manual")
    }

    /**
     * 输出缓存统计信息
     */
    private fun logCacheStats(operation: String) {
        val s = getCacheStats()
        if (s.error != null) {
            logger.warn("Failed to log cache stats: {}", s.error)
            return
        }
        val hitRateFormatted = "%.2f".format(s.hitRate)
        val loadTimeFormatted = "%.3f".format(s.averageLoadTimeMs)
        logger.info(
            "TokenManager Cache Stats [$operation] - Size: ${s.size}, HitRate: $hitRateFormatted%, Hits: ${s.hitCount}, Misses: ${s.missCount}, Evictions: ${s.evictionCount}, LoadTime: ${loadTimeFormatted}ms"
        )
    }

}