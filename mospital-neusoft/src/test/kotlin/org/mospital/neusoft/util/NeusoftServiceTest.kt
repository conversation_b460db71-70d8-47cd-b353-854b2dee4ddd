package org.mospital.neusoft.util

import org.dromara.hutool.core.date.TimeUtil
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.mospital.jackson.JacksonKit
import org.mospital.neusoft.mip.HisMipBaseRequest
import org.mospital.neusoft.mip.MipRefundRequest
import org.mospital.neusoft.request.*
import org.mospital.neusoft.service.NeusoftMipPayService
import java.time.LocalDateTime
import java.time.LocalTime

@Disabled
class NeusoftServiceTest {

    private val service = NeusoftService()

    @Test
    fun testQueryPatientInfo() {
        // 柏院萍 622223199604014125
        // 马敏 ********** **********
        val request = QueryPatientRequest().apply {
            patientCard = "037624307D3148C1B6117A975EF01BC6EF6380F4E03CE36E854B6137F98CCC6B"
        }
        val result = service.execute(request)
        println(JacksonKit.writeValueAsString(result))
    }

    @Test
    fun testFindAllDepartmentsResult() {
        val request = FindAllDepartmentsRequest()
        val result = service.execute(request)
        println(JacksonKit.writeValueAsString(result))
    }

    @Test
    fun testFindAllDoctorsResult() {
        val request = FindAllDoctorsRequest()
        val result = service.execute(request)
        println(JacksonKit.writeValueAsString(result))
    }

    @Test
    fun testQueryRealtimeScheduleDetail() {
        val request = QueryRealtimeScheduleDetailRequest().apply {
            clinicDate = "2023-08-11"
            departmentCode = "2012"
        }
        val result = service.execute(request)
        println(JacksonKit.writeValueAsString(result))
    }

    @Test
    fun testQueryRealtimeScheduleDetailInTestMode() {
        val request = QueryRealtimeScheduleDetailRequest().apply {
            clinicDate = "2023-08-11"
            departmentCode = "2012"
        }
        val result = service.execute(request = request, env = "test")
        println(JacksonKit.writeValueAsString(result))
    }

    @Test
    fun testQueryScheduleRequest() {
        val request = QueryScheduleRequest().apply {
            startDate = "2023-08-06"
            endDate = "2023-08-10"
        }
        val result = service.execute(request)
        println(JacksonKit.writeValueAsString(result))
    }

    @Test
    fun testQueryScheduleTimeRequest() {
        val request = QueryScheduleTimeRequest().apply {
            this.departmentCode = "2007"
            this.clinicDate = "2023-10-16"
            this.clinicTime = "上午"
            this.doctorCode = "None"
            this.scheduleId = "153676"
        }
        val result = service.execute(request).content.filter {
            val clinicDate = TimeUtil.parseDate(it.clinicDate, "yyyy/MM/dd 0:00:00")
            val clinicTime = LocalTime.parse(it.visitTime)
            val clinicDateTime = LocalDateTime.of(clinicDate, clinicTime)
            clinicDateTime > LocalDateTime.now()
        }
        println(JacksonKit.writeValueAsString(result))
    }

    @Test
    fun testQueryRealtimeScheduleRequest() {
        val request = QueryRealtimeScheduleRequest().apply {
            this.startDate = "2023-10-16"
            this.endDate = "2023-10-16"
            this.departmentCode = "2017"
        }
        val result = service.execute(request)
        println(JacksonKit.writeValueAsString(result))
    }

    @Test
    fun testAddPatientCardRequest() {
        val request = AddPatientCardRequest().apply {
            this.realName = "张永慧"
            this.idCard = "******************"
            this.idCardType = IdCardType.SHEN_FEN_ZHENG.code
            this.phone = "13137834876"
            this.nation = "汉族"
            this.sex = "女"
            this.address = "草原明珠30-4-502"
            this.erhcCardInfo128 = "037624307D3148C1B6117A975EF01BC6EF6380F4E03CE36E854B6137F98CCC6B"
            this.birthDay = "19901025"
            this.babyFlag = "0"
        }
        val result = service.execute(request)
        println(JacksonKit.writeValueAsString(result))
    }

    @Test
    fun testCheckPatientCardRequest() {
        val request = CheckPatientCardRequest().apply {
            this.idCard = "622223199604014125"
            patientCard = "**********"
        }
        val result = service.execute(request)
        println(JacksonKit.writeValueAsString(result))
    }

    @Test
    fun findInspectionReportRequest() {
        val request = FindInspectionReportRequest().apply {
            this.queryId = "**********"
            this.reportType = "0"
            this.startDate = "2010-01-01"
            this.endDate = "2023-09-12"
        }
        val result = service.execute(request)
        println(JacksonKit.writeValueAsString(result))
    }

    @Test
    fun findCompleteLISReportRequest() {
        val request = FindCompleteLISReportRequest().apply {
            this.reportId = "20230801G0130050"
            this.checkType = "1"
        }
        val result = service.execute(request)
        println(JacksonKit.writeValueAsString(result))
    }


    @Test
    fun queryInsertSubscriptionRequest() {
        val request = InsertSubscriptionRequest().apply {
            this.realName = "柏院萍"
            this.idCard = "622223199604014125"
        }
        val result = service.execute(request)
        println(JacksonKit.writeValueAsString(result))
    }

    @Test
    fun queryInHospitalListRequest() {
        val request = QueryInHospitalListRequest().apply {
            this.realName = "张兰香"
            this.idCard = "654124199212302521"
        }
        val result = service.execute(request)
        println(JacksonKit.writeValueAsString(result))
    }

    @Test
    fun getPatientExpense() {
        val request = GetPatientExpenseRequest().apply {
            this.queryId = "ZY010000126829"
        }
        val result = service.execute(request)
        println(JacksonKit.writeValueAsString(result))
    }

    @Test
    fun getDayExpense() {
        val request = GetDayExpenseRequest().apply {
            this.queryId = "ZY010000148304"
        }
        val result = service.execute(request)
        println(JacksonKit.writeValueAsString(result))


    }

    @Test
    fun getClinicBill() {
        val request = GetClinicBillRequest().apply {
            this.realName = "柏院萍"
            this.queryId = "**********"
            this.type = "1"
        }
        val result = service.execute(request)
        println(JacksonKit.writeValueAsString(result))
    }

    @Test
    fun getClinicDayExpense() {
        val request = GetClinicDayExpenseRequest().apply {
            this.realName = "柏院萍"
            this.patientCard = "**********"
            this.startDate = "2020-01-01"
            this.endDate = "2024-01-01"
        }
        val result = service.execute(request)
        println(JacksonKit.writeValueAsString(result))
    }

    @Test
    fun findOutpatientPayRequest() {
        val request = FindOutpatientPayRequest().apply {
            this.realName = "赵虎"
            this.patientCard = "**********"
        }
        val result = service.execute(request)
        println(JacksonKit.writeValueAsString(result))
    }

    @Test
    fun queryFeeItemInfoRequest() {
        val request = QueryFeeItemInfoRequest()
        val result = service.execute(request)
        println(JacksonKit.writeValueAsString(result))
    }

    @Test
    fun testQueryScheduleTestMode() {
        val request = QueryScheduleRequest().apply {
            startDate = "2024-04-26"
            endDate = "2024-04-30"
        }
        val result = service.execute(request)
        println(JacksonKit.writeValueAsString(result))
    }

    @Test
    fun testQueryScheduleTimeTestMode() {
        val request = QueryScheduleTimeRequest().apply {
            this.departmentCode = "2058"
            this.clinicDate = "2024-04-26"
            this.clinicTime = "下午"
            this.doctorCode = "None"
            this.scheduleId = "168968"
        }
        val result = service.execute(request = request, env = "test")
        println(JacksonKit.writeValueAsString(result))
    }

    @Test
    fun testQueryRealtimeScheduleTestMode() {
        val request = QueryRealtimeScheduleRequest().apply {
            this.startDate = "2024-04-21"
            this.endDate = "2024-04-21"
            this.departmentCode = "2007"
        }
        val result = service.execute(request)
        println(JacksonKit.writeValueAsString(result))
    }

    @Test
    fun testInsertSubscriptionTestMode() {
        val request = InsertSubscriptionRequest().apply {
            this.scheduleId = "168968"
            this.sourceNo = "9730230"
            this.sourceOrder = 21
            this.realName = "张雨轩"
            this.idCard = "621125199701013219"
            this.patientCard = "**********"
            this.departmentName = "助产士门诊"
            this.departmentCode = "2058"
            this.doctorName = "普通医生"
            this.doctorCode = "None"
            this.clinicType = "方便门诊"
            this.clinicDate = "2024/4/26 "
            this.clinicTime = "19:30:00"
            this.clinicFee = "0"
            this.payType = ""
            this.tradeNo = ""
            this.tradeDate = ""
        }
        service.execute(request = request, env = "test")
    }

    @Test
    fun testOutpatientPay() {
        val request = OutpatientPayRequest().apply {
            this.prescriptionNo = "7847124"
            this.realName = "贺永芳"
            this.patientCard = "**********"
            this.orderFee = "6.00"
            this.feeType = ""
            this.payType = "weChat"
            this.tradeNo = "GH202404211336491914"
            this.payedTime = "2024-04-21 13:36:50"
        }
        service.execute(request = request, env = "test")
    }

    @Test
    fun testFindOutpatientPayRequestTestMode() {
        val request = FindOutpatientPayRequest().apply {
            this.realName = "张雨轩"
            this.patientCard = "**********"
        }
        service.execute(request = request, env = "test")
    }

    @Test
    fun testMipRefund() {
        val request = MipRefundRequest(
            clinicNo = "4191829",
            payOrdId = "ORD650100202405031127160057052",
            payAuthNo = "AUTH650100202405031127108182834",
            ecToken = "",
            psnName = "贺永芳",
            psnIdenNo = "622428199803145825",
            insuCode = "650104",
            extParam = ""
        )
        NeusoftMipPayService.mipRefund(request = HisMipBaseRequest(request))
    }

    @Test
    fun testReleaseSubscription() {
        val request = ReleaseSubscriptionRequest().apply {
            this.scheduleId = "171491"
            this.subscriptionId = "4191600"

        }
        service.execute(request = request, env = "test")
    }

    @Test
    fun testSubscriptionHistory() {
        val request = SubscriptionHistoryRequest().apply {
            this.patientCard = "**********"
        }
        val subscriptionHistoryResult = service.execute(request, env = "test")
        println(JacksonKit.writeValueAsString(subscriptionHistoryResult))
    }

    @Test
    fun testGetIdTypes() {
        val request = GetIdCardTypesRequest()
        val result = service.execute(request = request)
        println(JacksonKit.writeValueAsString(result))
    }

    @Test
    fun testQueryInHospitalList() {
        val request = QueryInHospitalListRequest().apply {
            this.realName = "张莉萍"
            this.idCard = "65420219871114222X"
        }
        val result = service.execute(request)
        println(JacksonKit.writeValueAsString(result))
    }
}