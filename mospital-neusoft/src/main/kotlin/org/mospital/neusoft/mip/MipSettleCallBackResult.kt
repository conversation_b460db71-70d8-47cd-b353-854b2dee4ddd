package org.mospital.neusoft.mip


import com.fasterxml.jackson.annotation.JsonProperty

data class MipSettleCallBackResult(
    /**
     * HIS发票号
     */
    @JsonProperty("HisInvoiceNo")
    var hisInvoiceNo: String,
    /**
     * HIS结算时间
     */
    @JsonProperty("HisSetlTime")
    var hisSetlTime: String,
    /**
     * 医保结算Id
     */
    @JsonProperty("SiSetlId")
    var siSetlId: String,
    /**
     * 医保结算时间
     */
    @JsonProperty("SiSetlTime")
    var siSetlTime: String,
    /**
     * 扩展出参
     */
    @JsonProperty("ExtParam")
    var extParam: String? = null
)
