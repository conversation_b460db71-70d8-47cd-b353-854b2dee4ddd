package org.mospital.neusoft.mip

import com.fasterxml.jackson.annotation.JsonProperty

open class HisMipBaseResult<T>(
    @JsonProperty("OutData")
    var data: T? = null
) {
    /**
     * 0表示正常 ; 小于 表示腾讯服务出错; 大于0表示业务上出错
     * 响应的HTTP状态码如果不是200，说明发生系统级的错误。响应的状态码是200时，会保证报文结构会统一成下面的结构，方便做错误判断。
     */
    @JsonProperty("Code")
    var code: Int = 0

    @JsonProperty("Message")
    var message: String? = null

    @JsonProperty("ExtParam")
    var extParam: String? = null

    @JsonProperty("AppCode")
    var appCode: String? = null


    /**
     * 请求是否成功
     */
    open val isOk: Boolean
        get() = code == 0

    /**
     * 请求是否失败
     */
    val isFail: Boolean
        get() = !isOk

}
