package org.mospital.neusoft.mip

import java.math.BigDecimal

data class MipAccountsRequest(

    /**
     * 1：只按HIS结算时间，获取对账数据
     * 2：只按第三方支付时间，获取对账数据
     */
    var queryType: String,
    /**
     *格式：yyyy-MM-dd
     *QueryType为1时，本参数不参为空
     *意义同M002的出参HisSetlTime
     */
    var hisSetlTime: String?,
    /**
     * 格式：yyyy-MM-dd
     * QueryType为2时，本参数不参为空
     * 意义同M002的入参PayTime
     */
    var payTime: String?,

    var queryResultList: List<QueryResult>,
)

data class QueryResult(
    /**
     * 支付订单号
     */
    val payOrdId: String,
    /**
     * 医保就诊id
     */
    val mdtrtId: String,
    /**
     * 医保结算id
     */
    val siSetlId: String,
    /**
     * 支付类型
     * 见M002入参定义（27：支付宝医保，98：微信医保）
     * CancelFlag=’1’时，不能为空
     */
    val payWay: String,
    /**
     * 第三方自费订单号
     * CancelFlag=’1’时，此值为M002的同名入参
     * CancelFlag=’2’时，此值为第三方退费负交易的订单号
     */
    val orderId: String,
    /**
     * 第三方交易流水号
     * 见M002入参定义，
     * CancelFlag=’1’ and PayFee<>0时，不能为空
     * CancelFlag=’2’时，可为空
     */
    val tradeSerialNo: String,
    /**
     * 费用总额
     */
    val feeSumamt: BigDecimal,
    /**
     * 第三方支付金额
     * CancelFlag=’1’时，PayFee是M002的同名入参
     * CancelFlag=’2’时，PayFee是本次退费金额（为负数）
     */
    val payFee: BigDecimal,
    /**
     * 第三方支付时间
     * 格式为：yyyy-MM-dd HH:mm:ss
     * CancelFlag=’1’时，此值为M002的同名入参
     * CancelFlag=’2’时，此值为本次第三方退费时间
     */
    val payTime: String,
    /**
     * HIS结算时间
     * 格式为：yyyy-MM-dd HH:mm:ss
     * CancelFlag=’1’时，此值是M002的同名出参，不能为空
     * CancelFlag=’2’时，此值为HIS退费时间(如果能得到的话)
     *
     */
    val hisSetlTime: String,
    /**
     * HIS发票号
     * HIS结算虚拟发票号，见M002回参定义
     * CancelFlag=’1’时，不能为空
     * CancelFlag=’2’时，为空
     */
    val hisInvoiceNo: String,
    /**
     * 是否已退费标志
     * 1：缴费正交易，2：退费负交易
     */
    val cancelFlag: String,
    /**
     * CancelFlag=’1’时，此值为空
     * CancelFlag=’2’时，此值为对应正交易的第三方自费订单号
     */
    val srcOrderId: String?,
    /**
     * 姓名
     */
    val psnName: String,
    /**
     * 身份证号
     */
    val psnIdenNo: String,
    /**
     * 医保个人编号
     */
    val psnNo: String,
    /**
     * 扩展入参
     */
    val extParam: String? = null


)
