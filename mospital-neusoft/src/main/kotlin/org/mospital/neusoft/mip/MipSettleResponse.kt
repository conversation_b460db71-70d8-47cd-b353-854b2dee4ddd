package org.mospital.neusoft.mip

import com.fasterxml.jackson.annotation.JsonProperty
import java.math.BigDecimal


data class MipSettleResponse(
    /**
     * 支付订单号
     */
    @JsonProperty("PayOrdId")
    val payOrdId: String,
    /**
     * 支付token
     */
    @JsonProperty("PayToken")
    val payToken: String,
    /**
     * 医保就诊ID
     */
    @JsonProperty("MdtrtId")
    val mdtrtId: String,
    /**
     * 姓名
     */
    @JsonProperty("PsnName")
    val psnName: String,
    /**
     * 身份证号
     */
    @JsonProperty("PsnIdenNo")
    val psnIdenNo: String,
    /**
     * 医保个人编号
     */
    @JsonProperty("PsnNo")
    val psnNo: String,
    /**
     * 参保地区划
     */
    @JsonProperty("InsuCode")
    val insuCode: String,
    /**
     * 险种类型
     */
    @JsonProperty("InsuType")
    val insuType: String,
    /**
     * 医保预结算信息
     */
    @JsonProperty("PreSetlInfo")
    val preSetlInfo: PreSetlInfo,
    /**
     *医院HIS系统订单号
     */
    @JsonProperty("MedOrgOrd")
    var medOrgOrd: String,

    /**
     * 扩展入参
     */
    @JsonProperty("ExtParam")
    val extParam: String? = null
)

data class PreSetlInfo(
    /**
     * 费用总额
     */
    @JsonProperty("FeeSumamt")
    val feeSumamt: BigDecimal,
    /**
     * 医保基金支付
     */
    @JsonProperty("FundPay")
    val fundPay: BigDecimal,
    /**
     * 个人账户支出
     */
    @JsonProperty("PsnAcctPay")
    val psnAcctPay: BigDecimal,
    /**
     * 现金支付
     */
    @JsonProperty("OwnPayAmt")
    val ownPayAmt: BigDecimal,
    /**
     * 扩展出参
     */
    @JsonProperty("ExtParam")
    val extParam: String? = null
)





