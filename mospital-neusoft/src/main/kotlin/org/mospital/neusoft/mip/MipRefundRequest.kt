package org.mospital.neusoft.mip

import com.fasterxml.jackson.annotation.JsonProperty

data class MipRefundRequest(
    @JsonProperty("ClinicNo")
    val clinicNo: String,

    @JsonProperty("PayOrdId")
    val payOrdId: String,

    @JsonProperty("PayAuthNo")
    val payAuthNo: String,

    @JsonProperty("EcToken")
    val ecToken: String = "",

    @JsonProperty("PsnName")
    val psnName: String,

    @JsonProperty("PsnIdenNo")
    val psnIdenNo: String,

    @JsonProperty("InsuCode")
    val insuCode: String,

    @JsonProperty("ExtParam")
    val extParam: String
)
