package org.mospital.neusoft.mip

import com.fasterxml.jackson.annotation.JsonProperty

open class HisMipBaseRequest<T>(
    /**
     * 入参业务数据
     */
    @JsonProperty("InData")
    var inData: T? = null
) {

    /**
     * 接口编号
     */
    @JsonProperty("FunCode")
    var funCode: String = ""

    /**
     * 第三方应用类型
     * 值为“微信”或“支付宝”
     */
    @JsonProperty("ThirdType")
    var thirdType: String = "微信"

    /**
     * 第三方厂商代码
     *
     */
    @JsonProperty("AppCode")
    var appCode: String = "元启图灵"


    /**
     * 扩展入参
     */
    @JsonProperty("ExtParam")
    var extParam: ExtParam? = ExtParam(
        appID = NeusoftMipConfigs.INSTANCE.mipAppid,
        branchCode = NeusoftMipConfigs.INSTANCE.branchCode
    )

}


data class ExtParam(
    @JsonProperty("AppID")
    var appID: String,
    @JsonProperty("BranchCode")
    var branchCode: String?
)
