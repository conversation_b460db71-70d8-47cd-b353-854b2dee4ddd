package org.mospital.neusoft.mip

import com.fasterxml.jackson.annotation.JsonProperty
import java.math.BigDecimal

data class MipSettleStatusQueryResponse(
    /**
     * 订单状态编码
     */
    @JsonProperty("OrdStas")
    var ordStas: String,
    /**
     * 支付订单号
     */
    @JsonProperty("PayOrdId")
    var payOrdId: String,
    /**
     * 回调类型
     */
    @JsonProperty("CallType")
    var callType: String,
    /**
     * 结算类型
     */
    @JsonProperty("SetlType")
    var setlType: String,
    /**
     * 费用总金额
     */
    @JsonProperty("FeeSumAmt")
    var feeSumAmt: BigDecimal,
    /**
     * 现金支付
     */
    @JsonProperty("OwnPayAmt")
    var ownPayAmt: BigDecimal,
    /**
     * 医保收费时间
     */
    @JsonProperty("HIChrgTime")
    var hIChrgTime: String,
    /**
     * 扩展出参
     */
    @JsonProperty("ExtParam")
    var extParam: String? = null
)
