package org.mospital.neusoft.mip

import org.mospital.jackson.ConfigLoader

data class NeusoftMipConfig(
    val url: String
)

data class NeusoftMipConfigs(
    val environments: Map<String, NeusoftMipConfig>,
    val mipAppid: String,
    val branchCode: String
) {

    companion object {
        const val DEFAULT_ENV: String = "prod" // 默认环境

        val INSTANCE: NeusoftMipConfigs by lazy {
            ConfigLoader.loadConfig<NeusoftMipConfigs>("neusoft-mip.yaml", NeusoftMipConfigs::class.java)
                .also { config ->
                    require(config.environments.isNotEmpty()) { "No environments defined in neusoft-mip.yaml" }
                    require(config.mipAppid.isNotBlank()) { "mipAppid must not be blank in neusoft-mip.yaml" }
                }
        }
    }

    fun getConfigByEnv(env: String): NeusoftMipConfig {
        return environments[env]
            ?: throw IllegalArgumentException("Unsupported environment: $env. Available environments: ${getAvailableEnvironments()}. Check neusoft-mip.yaml.")
    }

    fun getAvailableEnvironments(): Set<String> {
        return environments.keys
    }

}
