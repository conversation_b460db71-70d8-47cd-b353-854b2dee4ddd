package org.mospital.neusoft.mip

import java.math.BigDecimal

data class MipSettleCallBackRequest(

    /**
     *挂号流水号
     */
    var clinicNo: String,

    /**
     *结算处方信息
     */
    var recipeFeeList: List<RecipeFee>,

    /**
     *费用总额
     */
    var totalFee: BigDecimal,

    /**
     *支付订单号
     */
    var payOrdId: String,

    /**
     *患者姓名
     */
    var psnName: String,

    /**
     *患者身份证号
     */
    var psnIdenNo: String,

    /**
     *第三方交易信息
     */
    var thirdInfo: ThirdInfoDetail?,

    /**
     *扩展入参
     */
    var extParam: String?
) {
    /**
     *结算处方信息
     */
    data class RecipeFee(
        /**
         *处方号
         */
        var recipeNo: String,
        /**
         *费用 单位分
         */
        var fee: BigDecimal
    )

    /**
     *第三方交易信息
     */
    data class ThirdInfoDetail(
        /**
         *第三方的自费订单号
         */
        var orderId: String,
        /**
         *第三方交易流水号
         */
        var tradeSerialNo: String,
        /**
         *支付类型ID
         * 27：支付宝医保，98：微信医保
         */
        var payWay: String,
        /**
         *第三方支付金额
         */
        var payFee: BigDecimal,
        /**
         *第三方支付时间
         */
        var payTime: String,
        /**
         *扩展入参
         */
        var extParam: Any?
    )


    /**
     * 在医保上传结算后第一次构造入参
     * 缺少thirdInfo 需要在支付渠道下单后补充
     */
    constructor(mipSettleRequest: MipSettleRequest, mipSettleResponse: MipSettleResponse) : this(
        clinicNo = mipSettleRequest.clinicNo,
        recipeFeeList = mipSettleRequest.recipeFeeList.map { RecipeFee(it.recipeNo, it.fee) },
        totalFee = mipSettleResponse.preSetlInfo.feeSumamt,
        payOrdId = mipSettleResponse.payOrdId,
        psnName = mipSettleResponse.psnName,
        psnIdenNo = mipSettleResponse.psnIdenNo,
        thirdInfo = null,
        extParam = null
    )


}





