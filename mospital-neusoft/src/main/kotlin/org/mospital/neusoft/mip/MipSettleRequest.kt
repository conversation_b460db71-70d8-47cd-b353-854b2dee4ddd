package org.mospital.neusoft.mip

import java.math.BigDecimal

data class MipSettleRequest(
    /**
     * 挂号流水号
     * HIS门诊挂号时的挂号流水号
     */

    var clinicNo: String,
    var recipeFeeList: List<RecipeFee>,
    /**
     * 费用总额
     * 本次待缴费处方明细的总金额
     *要求等于RecipeFeeList中各处方金额之和
     */
    var totalFee: BigDecimal,
    /**
     * 和EcToken不能同时为空
     */
    var payAuthNo: String? = null,
    /**
     * 和PayAuthNo不能同时为空
     */
    var ecToken: String? = null,
    /**
     * 患者姓名
     */
    var psnName: String,
    /**
     * 患者身份证号
     */
    var psnIdenNo: String,
    /**
     * 患者参保地区划
     * 为空时，HIS则会在院端按身份证号取该患者最近一次就诊时的参保地区划。若患者为首次时，HIS将默认取值为就医地医保区划，即只能针对本地患者进行移动支付系列操作
     */
    var insuCode: String? = null,
    /**
     * 医疗类别代码 目前仅支付类别：11-普通门诊
     */
    var medType: String = "11",
    /**
     * 个人账户使用标志 0 否 1 是
     */
    var acctUsedFlag: String,
    /**
     *经纬度 格式:经度,纬度 如：118.096435,24.485407，
     *定位失败情况下传：0,0，此值为空时，HIS将默认为0,0
     */
    var uldLatlnt: String = "0,0",

    /**
     * 扩展入参
     */
    var extParam: String? = null

) {
    data class RecipeFee(
        /**
         * HIS处方号
         */
        var recipeNo: String,
        /**
         * 本张处方明细的总金额
         */
        var fee: BigDecimal,
        var extParam: String? = null,
    )

}








