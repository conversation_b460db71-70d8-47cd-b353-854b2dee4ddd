package org.mospital.neusoft.mip

import com.fasterxml.jackson.annotation.JsonProperty
import java.math.BigDecimal

data class MipRefundResult(
    @JsonProperty("PayOrdId")
    var payOrdId: String,

    @JsonProperty("HisInvoiceNo")
    var hisInvoiceNo: String,

    @JsonProperty("HisSetlTime")
    var hisSetlTime: String,

    @JsonProperty("SiSetlId")
    var siSetlId: String,

    @JsonProperty("SiSetlTime")
    var siSetlTime: String,

    @JsonProperty("MdtrtId")
    var mdtrtId: String,

    @JsonProperty("MedOrgOrd")
    var medOrgOrd: String,

    @JsonProperty("SIRNSetlInfo")
    var sirnSetlInfo: SirnSetlInfo,

    @JsonProperty("ExtParam")
    var extParam: String? = null
) {
    data class SirnSetlInfo(
        @JsonProperty("FeeSumamt")
        var feeSumant: BigDecimal,

        @JsonProperty("FundPay")
        var fundPay: BigDecimal,

        @JsonProperty("PsnAcctPay")
        var psnAcctPay: BigDecimal,

        @JsonProperty("OwnPayAmt")
        var ownPayAmt: BigDecimal,

        @JsonProperty("ExtParam")
        var extParam: String? = null
    )
}