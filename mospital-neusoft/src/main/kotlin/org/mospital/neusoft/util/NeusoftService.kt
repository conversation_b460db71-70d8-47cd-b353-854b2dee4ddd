package org.mospital.neusoft.util

import org.dromara.hutool.core.lang.Singleton
import org.dromara.hutool.http.client.ClientConfig
import org.dromara.hutool.http.client.engine.ClientEngine
import org.dromara.hutool.http.client.engine.ClientEngineFactory
import org.dromara.hutool.http.webservice.SoapClient
import org.mospital.common.IdUtil
import org.mospital.common.jaxb.JAXBKit
import org.mospital.neusoft.request.BaseRequest
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class NeusoftService {

    companion object {
        val log: Logger = LoggerFactory.getLogger(NeusoftService::class.java)

        @Suppress("unused")
        val me = NeusoftService()
    }

    init {
        // 强制使用 OkHttp 引擎
        val okHttpEngine = ClientEngineFactory.createEngine("OkHttp")
        okHttpEngine.init(ClientConfig.of().setConnectionTimeout(5_000).setReadTimeout(30_000))
        Singleton.put(ClientEngine::class.java.name, okHttpEngine)
    }

    fun <T> execute(request: BaseRequest<T>, env: String = NeusoftHisConfigs.DEFAULT_ENV): T {
        val requestId = IdUtil.simpleUUID()
        val startTime = System.currentTimeMillis()

        // 获取配置
        val config: NeusoftHisConfig = try {
            NeusoftHisConfigs.INSTANCE.getConfigByEnv(env)
        } catch (e: Exception) {
            log.error("Failed to load configuration for environment: $env", e)
            throw RuntimeException("Configuration not found for environment: $env", e)
        }
        val url: String = config.url
        val xmlns: String = config.xmlns

        val requestXml = JAXBKit.marshal(request)
        log.info("[${request.method}:$requestId]url=$url")
        log.info("[${request.method}:$requestId]request=" + requestXml)

        try {
            val responseXml = SoapClient.of(url)
                .header("SOAPAction", "\"${xmlns}${request.method}\"")
                .setMethod("tem:${request.method}", xmlns)
                .setParam("xml", requestXml)
                .send()
                .bodyText
            val costTime = String.format("%.3f", (System.currentTimeMillis() - startTime) / 1000.0)
            log.info("[${request.method}:$requestId]response=$responseXml")
            log.info("[${request.method}:$requestId]costTime=${costTime}s")
            return JAXBKit.unmarshal(request.responseClass, responseXml)
        } catch (e: Exception) {
            val costTime = String.format("%.3f", (System.currentTimeMillis() - startTime) / 1000.0)
            log.error("[${request.method}:$requestId]error=${e.message}, costTime=${costTime}s", e)
            throw RuntimeException(e)
        }

    }

}