package org.mospital.neusoft.util

import org.mospital.jackson.ConfigLoader

data class NeusoftHisConfig(
    val url: String,
    val xmlns: String
)

data class NeusoftHisConfigs(
    val environments: Map<String, NeusoftHisConfig>
) {

    companion object {
        const val DEFAULT_ENV: String = "prod" // 默认环境

        val INSTANCE: NeusoftHisConfigs by lazy {
            ConfigLoader.loadConfig<NeusoftHisConfigs>("neusoft-his.yaml", NeusoftHisConfigs::class.java)
                .also { config ->
                    require(config.environments.isNotEmpty()) { "No environments defined in neusoft-his.yaml" }
                    config.environments.values.forEach { env ->
                        require(env.url.isNotBlank()) { "Environment URL must not be blank in neusoft-his.yaml" }
                        require(env.xmlns.isNotBlank()) { "Environment xmlns must not be blank in neusoft-his.yaml" }
                    }
                }
        }
    }

    fun getConfigByEnv(env: String): NeusoftHisConfig {
        return environments[env]
            ?: throw IllegalArgumentException("Unsupported environment: $env. Available environments: ${getAvailableEnvironments()}. Check neusoft-his.yaml.")
    }

    fun getAvailableEnvironments(): Set<String> {
        return environments.keys
    }

}