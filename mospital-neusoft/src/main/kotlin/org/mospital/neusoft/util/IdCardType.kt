package org.mospital.neusoft.util

@Suppress("unused")
enum class IdCardType(
    val code: String,
    val description: String,
) {

    SHEN_FEN_ZHENG("01", "身份证"),
    YONG_JU_ZHENG("06", "永居证"),
    HU_ZHAO("07", "护照"),

    // HIS不支持，健康卡支持
    CHU_SHENG_ZHENG("08", "出生医学证明");

    companion object {
        fun fromCode(code: String): IdCardType? {
            return IdCardType.entries.firstOrNull { it.code == code }
        }
    }

}