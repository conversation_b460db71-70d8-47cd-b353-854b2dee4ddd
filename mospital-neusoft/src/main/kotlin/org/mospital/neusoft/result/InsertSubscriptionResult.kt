package org.mospital.neusoft.result

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement


/**
 * 5.1.	新增预约记录
 */
@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.PROPERTY)
class InsertSubscriptionResult : BaseResult() {

    /**
     * 预约记录编号
     */
    @get:XmlElement(name = "subscriptionId")
    var subscriptionId: String? = null

    /**
     * 订单金额
     */
    @get:XmlElement(name = "amount")
    var amount: String? = null

}