package org.mospital.neusoft.result

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement


/**
 * 10.1.	预缴住院费用
 */
@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.PROPERTY)
class PrePayExpenseResult : BaseResult() {

    /**
     * 发票号
     */
    @get:XmlElement(name = "invoiceNo")
    var invoiceNo: String = ""

    /**
     * HIS 支付流水号
     */
    @get:XmlElement(name = "serialNo")
    var serialNo: String = ""

}
