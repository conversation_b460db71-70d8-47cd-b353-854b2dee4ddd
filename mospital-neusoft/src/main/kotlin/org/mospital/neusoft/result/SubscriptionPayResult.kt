package org.mospital.neusoft.result

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement


/**
 * 5.3.	预约支付
 */
@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.PROPERTY)
class SubscriptionPayResult : BaseResult() {

    /**
     * 就诊号
     */
    @get:XmlElement(name = "clinicNo")
    var clinicNo: String? = null

    /**
     * 发票号
     */
    @get:XmlElement(name = "invoiceNo")
    var invoiceNo: String = ""

    /**
     * 挂号记录主键
     */
    @get:XmlElement(name = "registerId")
    var registerId: String? = null

    /**
     * 就诊排队序号
     */
    @get:XmlElement(name = "hisOrderNumber")
    var hisOrderNumber: String? = null

    /**
     * 是否需要退款
     */
    @get:XmlElement(name = "isRefund")
    var isRefund: String? = null

}