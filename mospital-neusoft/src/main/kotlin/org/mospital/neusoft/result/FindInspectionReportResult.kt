package org.mospital.neusoft.result

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement


/**
 * 7.1.	查询检验、检查报告列表(默认查询一个月内的报告)
 */
@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.PROPERTY)
class FindInspectionReportResult : BaseResult() {

    @get:XmlElement(name = "content")
    var content: MutableList<InspectionReport> = mutableListOf()

    class InspectionReport {

        /**
         * 报告 ID
         */
        @get:XmlElement(name = "reportId")
        var reportId: String? = null

        /**
         * 报告名称
         */
        @get:XmlElement(name = "reportName")
        var reportName: String? = null

        /**
         * HIS 挂号记录 ID
         */
        @get:XmlElement(name = "hisRegisterId")
        var hisRegisterId: String? = null

        /**
         * 姓名
         */
        @get:XmlElement(name = "realName")
        var realName: String? = null

        /**
         * 年龄
         */
        @get:XmlElement(name = "age")
        var age: String? = null

        /**
         * 检查类型
         */
        @get:XmlElement(name = "checkType")
        var checkType: String? = null

        /**
         * 检查日期
         */
        @get:XmlElement(name = "checkDate")
        var checkDate: String? = null

        /**
         * 报告状态
         */
        @get:XmlElement(name = "status")
        var status: String? = null

        /**
         * 诊断代码
         */
        @get:XmlElement(name = "diagnosisCode")
        var diagnosisCode: String? = null

        /**
         * 诊断名称
         */
        @get:XmlElement(name = "diagnosisName")
        var diagnosisName: String? = null
    }
}