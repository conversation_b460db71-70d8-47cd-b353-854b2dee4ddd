package org.mospital.neusoft.result

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement


/**
 * 预约挂号接口响应类
 */
@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.PROPERTY)
class AppointmentInfo : BaseResult() {
    /**
     * 医生编号
     */
    @get:XmlElement(name = "doctorCode")
    var doctorCode: String = ""

    /**
     * 科室编号
     */
    @get:XmlElement(name = "departmentCode")
    var departmentCode: String = ""

    /**
     * 预约日期
     */
    @get:XmlElement(name = "clinicDate")
    var clinicDate: String = ""

    /**
     * 号源编号
     */
    @get:XmlElement(name = "sourceNo")
    var sourceNo: String = ""

    /**
     * 号源序号
     */
    @get:XmlElement(name = "sourceOrder")
    var sourceOrder: String = ""

    /**
     * 就诊时间点
     */
    @get:XmlElement(name = "visitTime")
    var visitTime: String = ""

    /**
     * 是否有号
     */
    @get:XmlElement(name = "havaNo")
    var haveNo: String = ""
}
