package org.mospital.neusoft.result

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement


/**
 * 5.3.	预约支付
 */
@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.PROPERTY)
class SubscriptionHistoryResult : BaseResult() {
    @get:XmlElement(name = "content")
    var content: MutableList<QueryInfo> = mutableListOf()


    class QueryInfo {
        /**
         * 挂号 ID
         */
        @get:XmlElement(name = "registerId")
        var registerId: String = ""

        /**
         * HIS 订单号
         */
        @get:XmlElement(name = "hisOrderNumber")
        var hisOrderNumber: String = ""

        /**
         * 金额
         */
        @get:XmlElement(name = "amount")
        var amount: String = ""

        /**
         * 医生
         */
        @get:XmlElement(name = "doctor")
        var doctor: String = ""

        /**
         * 科室名称
         */
        @get:XmlElement(name = "departmentName")
        var departmentName: String = ""

        /**
         * 就诊日期
         */
        @get:XmlElement(name = "clinicDate")
        var clinicDate: String = ""

        /**
         * 就诊时间
         */
        @get:XmlElement(name = "clinicTime")
        var clinicTime: String = ""

        /**
         * 就诊状态
         */
        @get:XmlElement(name = "state")
        var state: String = ""
    }

}