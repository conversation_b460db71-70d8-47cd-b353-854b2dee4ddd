package org.mospital.neusoft.result

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement


@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.PROPERTY)
class GetPatientExpenseResult : BaseResult() {
    /** 姓名 */
    @get:XmlElement(name = "realName")
    var realName: String? = null

    /** 就诊号 */
    @get:XmlElement(name = "admission_number")
    var admissionNumber: String? = null

    /** 住院号 */
    @get:XmlElement(name = "serialNumber")
    var serialNumber: String? = null

    /** 已预缴住院金额 */
    @get:XmlElement(name = "prepayAmt")
    var prepayAmt: String? = null

    /** 已经使用金额 */
    @get:XmlElement(name = "usedAmt")
    var usedAmt: String? = null

    /** 剩余金额 */
    @get:XmlElement(name = "remainAmt")
    var remainAmt: String? = null

    /** 住院科室 */
    @get:XmlElement(name = "HouseDeptName")
    var houseDeptName: String? = null
}
