package org.mospital.neusoft.result

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "Response")
@XmlAccessorType(XmlAccessType.PROPERTY)
class GetIdCardTypesResult : BaseResult() {

    @get:XmlElement(name = "Result")
    override var result: String = ""

    override val isOk: Boolean
        get() = "1" == result

    @get:XmlElement(name = "IDCardTypeList")
    var list: MutableList<IdCardType> = mutableListOf()

    class IdCardType {

        @get:XmlElement(name = "Name")
        var name: String = ""

        @get:XmlElement(name = "Code")
        var code: String = ""

    }

}