package org.mospital.neusoft.result

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement


/**
 * 3.1.	获取排班
 */
@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.PROPERTY)
class QueryScheduleResult : BaseResult() {

    @get:XmlElement(name = "content")
    var content: MutableList<ScheduleInfo> = mutableListOf()

    class ScheduleInfo {
        /**
         * 排班主键
         */
        @get:XmlElement(name = "scheduleId")
        var scheduleId: String = ""

        /**
         * 医生编号
         */
        @get:XmlElement(name = "doctorCode")
        var doctorCode: String = ""

        /**
         * 所属科室编码
         */
        @get:XmlElement(name = "departmentCode")
        var departmentCode: String = ""

        /**
         * 医生排班日期
         */
        @get:XmlElement(name = "clinicDate")
        var clinicDate: String = ""

        /**
         * 排班时间段（上午/下午）
         */
        @get:XmlElement(name = "clinicTime")
        var clinicTime: String = ""

        /**
         * 排班星期（周一到周日）
         */
        @get:XmlElement(name = "clinicWeek")
        var clinicWeek: String = ""

        /**
         * 门诊类型（普通门诊/专家门诊）
         */
        @get:XmlElement(name = "clinicType")
        var clinicType: String = ""

        /**
         * 剩余号源数
         */
        @get:XmlElement(name = "clinicNo")
        var clinicNo: String = ""

        @get:XmlElement(name = "total")
        var total: String = ""

        @get:XmlElement(name = "clinicTimeQuantum")
        var clinicTimeQuantum: String = ""

        @get:XmlElement(name = "clinicFee")
        var clinicFee: String = ""


        @get:XmlElement(name = "status")
        var status: String = ""
    }
}