package org.mospital.neusoft.result

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement


/**
 * 7.2.	查询检验报告详情
 */
@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.PROPERTY)
class FindCompleteLISReportResult : BaseResult() {

    @get:XmlElement(name = "content")
    var content: MutableList<InspectionReport> = mutableListOf()

    class InspectionReport {

        /**
         * 检查日期
         */
        @get:XmlElement(name = "checkDate")
        var checkDate: String? = null

        /**
         * 报告类型
         */
        @get:XmlElement(name = "type")
        var type: String? = null

        /**
         * 检验项目名称
         */
        @get:XmlElement(name = "itemName")
        var itemName: String? = null

        /**
         * 样本类型
         */
        @get:XmlElement(name = "sample")
        var sample: String? = null

        /**
         * 实际结果
         */
        @get:XmlElement(name = "realResult")
        var realResult: String? = null

        /**
         * 参考值
         */
        @get:XmlElement(name = "referprint")
        var referprint: String? = null

        /**
         * 结论
         */
        @get:XmlElement(name = "conclusion")
        var conclusion: String? = null

    }
}