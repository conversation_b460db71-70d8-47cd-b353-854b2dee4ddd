package org.mospital.neusoft.result

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement


@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.PROPERTY)
class QueryPatientResult : BaseResult() {

    @get:XmlElement(name = "realName")
    var realName: String = ""

    @get:XmlElement(name = "idCard")
    var idCard: String = ""

    @get:XmlElement(name = "phone")
    var phone: String = ""

    @get:XmlElement(name = "sex")
    var sex: String = ""

    @get:XmlElement(name = "birthday")
    var birthday: String = ""

    @get:XmlElement(name = "eCardBalance")
    var eCardBalance: String = "0"

    @get:XmlElement(name = "hisNo")
    var hisNo: String = ""

}