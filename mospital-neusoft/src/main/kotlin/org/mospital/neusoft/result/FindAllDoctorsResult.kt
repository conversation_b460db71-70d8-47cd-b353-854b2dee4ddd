package org.mospital.neusoft.result

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement


/**
 * 2.2.	获取医生列表信息
 */
@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.PROPERTY)
class FindAllDoctorsResult : BaseResult() {

    @get:XmlElement(name = "content")
    var content: MutableList<Doctor> = mutableListOf()

    class Doctor {
        /**
         * 医生姓名
         */
        @get:XmlElement(name = "doctorName")
        var doctorName: String = ""

        /**
         * 医生编号
         */
        @get:XmlElement(name = "doctorCode")
        var doctorCode: String = ""

        /**
         * 学术职称
         * 非必填
         */
        @get:XmlElement(name = "jobTitleId")
        var jobTitleId: String = ""

        @get:XmlElement(name = "oper_date")
        var operDate: String = ""

        /**
         * 医生头像
         * 非必填
         */
        @get:XmlElement(name = "doctorAvatar")
        var doctorAvatar: String = ""

        /**
         * 医生所属科室编码
         */
        @get:XmlElement(name = "departmentCode")
        var departmentCode: String = ""

        /**
         * 医生所属科室名称
         */
        @get:XmlElement(name = "departmentName")
        var departmentName: String = ""

        /**
         * 医生所属医院编码
         */
        @get:XmlElement(name = "hospitalCode")
        var hospitalCode: String = ""

        /**
         * 医生所属医院名称
         */
        @get:XmlElement(name = "hospitalName")
        var hospitalName: String = ""

        /**
         * 医生简介
         * 非必填
         */
        @get:XmlElement(name = "doctorSummary")
        var doctorSummary: String = ""

        /**
         * 医生擅长
         * 非必填
         */
        @get:XmlElement(name = "doctorSpecialty")
        var doctorSpecialty: String = ""

        /**
         * 是否有效
         */
        @get:XmlElement(name = "valid_state")
        var validState: String = ""

        /**
         * 医生身份证号
         */
        @get:XmlElement(name = "IdNo")
        var idNo: String = ""
    }
}