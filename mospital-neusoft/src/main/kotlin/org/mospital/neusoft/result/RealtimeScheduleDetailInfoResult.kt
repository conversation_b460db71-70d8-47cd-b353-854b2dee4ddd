package org.mospital.neusoft.result

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement


/**
 * 医生实时排班详情查询接口响应类
 */
@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.PROPERTY)
class RealtimeScheduleDetailInfoResult : BaseResult() {

    @get:XmlElement(name = "content")
    var content: MutableList<RealtimeScheduleDetailInfo> = mutableListOf()

    class RealtimeScheduleDetailInfo {
        /**
         * 排班主键
         */
        @get:XmlElement(name = "scheduleId")
        var scheduleId: String = ""

        /**
         * 医生编号
         */
        @get:XmlElement(name = "doctorCode")
        var doctorCode: String = ""

        /**
         * 所属科室编码
         */
        @get:XmlElement(name = "departmentCode")
        var departmentCode: String = ""

        /**
         * 医生排班日期
         */
        @get:XmlElement(name = "clinicDate")
        var clinicDate: String = ""

        /**
         * 排班时间段（上午/下午）
         */
        @get:XmlElement(name = "clinicTime")
        var clinicTime: String = ""

        /**
         * 排班星期（周一到周日）
         */
        @get:XmlElement(name = "clinicWeek")
        var clinicWeek: String = ""

        /**
         * 门诊类型（普通门诊/专家门诊）
         */
        @get:XmlElement(name = "clinicType")
        var clinicType: String = ""

        /**
         * 剩余号源数
         */
        @get:XmlElement(name = "clinicNo")
        var clinicNo: String = ""

        /**
         * 总号源数
         * 非必填
         */
        @get:XmlElement(name = "total")
        var total: String = ""

        /**
         * 排班时间段
         * 非必填
         */
        @get:XmlElement(name = "clinicTimeQuantum")
        var clinicTimeQuantum: String = ""

        /**
         * 门诊费用
         * 非必填
         */
        @get:XmlElement(name = "clinicFee")
        var clinicFee: String = ""

        /**
         * 开诊状态（1 或空开诊;0-停诊）
         * 非必填
         */
        @get:XmlElement(name = "status")
        var status: String = ""

        /**
         * 医生姓名
         * 非必填
         */
        @get:XmlElement(name = "doctorName")
        var doctorName: String = ""

        /**
         * 医生职称
         * 非必填
         */
        @get:XmlElement(name = "doctorTitle")
        var doctorTitle: String = ""

        /**
         * 医生头像
         * 非必填
         */
        @get:XmlElement(name = "doctorAvatar")
        var doctorAvatar: String = ""

        /**
         * 科室名称
         * 非必填
         */
        @get:XmlElement(name = "departmentName")
        var departmentName: String = ""

        /**
         * 科室地址
         * 非必填
         */
        @get:XmlElement(name = "departmentAddress")
        var departmentAddress: String = ""

        /**
         * 科室电话
         * 非必填
         */
        @get:XmlElement(name = "departmentPhone")
        var departmentPhone: String = ""

        /**
         * 医院名称
         * 非必填
         */
        @get:XmlElement(name = "hospitalName")
        var hospitalName: String = ""

        /**
         * 医院地址
         * 非必填
         */
        @get:XmlElement(name = "hospitalAddress")
        var hospitalAddress: String = ""

        /**
         * 医院电话
         * 非必填
         */
        @get:XmlElement(name = "hospitalPhone")
        var hospitalPhone: String = ""

        /**
         * 超过65岁老人诊查费
         */
        @get:XmlElement(name = "OverRegFee")
        var overRegFee: String = ""

        /**
         * 不超过65岁老人诊查费
         */
        @get:XmlElement(name = "UnderRegFee")
        var underRegFee: String = ""

        /**
         * 非HIS返回字段，根据实际年龄计算金额，
         */
        @get:XmlElement(name = "realRegFee")
        var realRegFee: String = ""

        @get:XmlElement(name = "positionId")
        var positionId: String = ""

        @get:XmlElement(name = "positionName")
        var positionName: String = ""
    }
}