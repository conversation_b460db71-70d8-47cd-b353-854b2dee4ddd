package org.mospital.neusoft.result

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

/**
 * 4.4.	住院病人信息查询
 */
@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.PROPERTY)
class QueryInHospitalPatientResult : BaseResult() {

    @get:XmlElement(name = "data")
    var dataList: MutableList<Data> = mutableListOf()

    class Data {

        /**
         * 就诊人姓名
         */
        @get:XmlElement(name = "realName")
        var realName: String? = null

        /**
         * 就诊人身份证号
         */
        @get:XmlElement(name = "idCard")
        var idCard: String? = null

        /**
         * 联系电话
         */
        @get:XmlElement(name = "phone")
        var phone: String? = null

        /**
         * 性别
         */
        @get:XmlElement(name = "sex")
        var sex: String? = null

        /**
         * 出生日期
         */
        @get:XmlElement(name = "birthday")
        var birthday: String? = null

        /**
         * 已缴押金金额
         */
        @get:XmlElement(name = "totleAmt")
        var totleAmt: String? = null

        /**
         * 已使用金额
         */
        @get:XmlElement(name = "usedAmt")
        var usedAmt: String? = null

        /**
         * 剩余金额
         */
        @get:XmlElement(name = "remainAmt")
        var remainAmt: String? = null

        /**
         * 状态。0出院，1在院
         */
        @get:XmlElement(name = "status")
        var status: Int? = null

        /**
         * 入院日期
         */
        @get:XmlElement(name = "inhospitalDate")
        var inhospitalDate: String? = null

        /**
         * 出院日期
         */
        @get:XmlElement(name = "leaveDate")
        var leaveDate: String? = null

        /**
         * 病区
         */
        @get:XmlElement(name = "inpatientArea")
        var inpatientArea: String? = null

        /**
         * 住院科室
         */
        @get:XmlElement(name = "departmentCode")
        var departmentCode: String? = null

        /**
         * 管床医生
         */
        @get:XmlElement(name = "doctorCode")
        var doctorCode: String? = null

        /**
         * 门诊号
         */
        @get:XmlElement(name = "hisNo")
        var hisNo: String? = null

        /**
         * 住院流水号
         */
        @get:XmlElement(name = "inpatientNo")
        var inpatientNo: String? = null

        /**
         * 住院号
         */
        @get:XmlElement(name = "serialNumber")
        var serialNumber: String? = null
    }
}