package org.mospital.neusoft.result

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement


/**
 * 4.1.	在线建卡
 */
@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.PROPERTY)
class AddPatientCardResult : BaseResult() {

    /**
     * 就诊卡号
     */
    @get:XmlElement(name = "patientCard")
    var patientCard: String = ""

    /**
     * HIS 患者唯一标识，病历号、登记号等
     */
    @get:XmlElement(name = "hisNo")
    var hisNo: String = ""

}
