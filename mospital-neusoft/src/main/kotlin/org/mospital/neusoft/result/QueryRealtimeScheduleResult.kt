package org.mospital.neusoft.result

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement


/**
 * 3.2.	医生坐诊信息查询（实时排班使用）
 */
@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.PROPERTY)
class QueryRealtimeScheduleResult : BaseResult() {

    @get:XmlElement(name = "content")
    var content: MutableList<RealtimeSchedule> = mutableListOf()

    class RealtimeSchedule {
        /**
         * 排班主键
         */
        @get:XmlElement(name = "scheduleId")
        var scheduleId: String = ""

        /**
         * 医生编号
         */
        @get:XmlElement(name = "doctorCode")
        var doctorCode: String = ""

        /**
         * 医生姓名
         */
        @get:XmlElement(name = "doctorName")
        var doctorName: String = ""

        /**
         * 医生职称代码（专家必填）
         */
        @get:XmlElement(name = "positionId")
        var positionId: String = ""

        /**
         * 医生职称名称（专家必填）
         */
        @get:XmlElement(name = "positionName")
        var positionName: String = ""

        /**
         * 所属科室编码
         */
        @get:XmlElement(name = "departmentCode")
        var departmentCode: String = ""

        /**
         * 医生排班日期
         */
        @get:XmlElement(name = "clinicDate")
        var clinicDate: String = ""

        /**
         * 排班时间段（上午/下午）
         */
        @get:XmlElement(name = "clinicTime")
        var clinicTime: String = ""

        /**
         * 排班星期（周一到周日）
         */
        @get:XmlElement(name = "clinicWeek")
        var clinicWeek: String = ""

        /**
         * 门诊类型（普通门诊/专家门诊）
         */
        @get:XmlElement(name = "clinicType")
        var clinicType: String = ""

        /**
         * 剩余号源数
         */
        @get:XmlElement(name = "clinicNo")
        var clinicNo: String = ""

        /**
         * 总号源数
         * 非必填
         */
        @get:XmlElement(name = "total")
        var total: String = ""

        /**
         * 排班时间段
         * 非必填
         */
        @get:XmlElement(name = "clinicTimeQuantum")
        var clinicTimeQuantum: String = ""

        /**
         * 门诊费用
         * 非必填
         */
        @get:XmlElement(name = "clinicFee")
        var clinicFee: String = ""

        /**
         * 开诊状态（1 或空开诊;0-停诊）
         * 非必填
         */
        @get:XmlElement(name = "status")
        var status: String = ""
    }
}