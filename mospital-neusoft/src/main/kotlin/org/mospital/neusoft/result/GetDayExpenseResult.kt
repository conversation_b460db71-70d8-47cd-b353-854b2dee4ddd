package org.mospital.neusoft.result

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

/**
 * 9.2.	获取某日住院费用清单 响应类
 */
@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.PROPERTY)
class GetDayExpenseResult : BaseResult() {

    @get:XmlElement(name = "data")
    var dataList: MutableList<Data> = mutableListOf()

    class Data {
        /**
         * 费用类型
         */
        @get:XmlElement(name = "feeType")
        var feeType: String? = null

        /**
         * 总金额
         */
        @get:XmlElement(name = "amount")
        var amount: String? = null

        /**
         * 数量
         */
        @get:XmlElement(name = "count")
        var count: String? = null

        /**
         * 单价
         */
        @get:XmlElement(name = "price")
        var price: String? = null

        /**
         * 费用日期
         */
        @get:XmlElement(name = "feeDate")
        var feeDate: String? = null
    }

}
