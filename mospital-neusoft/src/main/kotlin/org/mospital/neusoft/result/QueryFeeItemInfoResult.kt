package org.mospital.neusoft.result

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

/**
 * 物价查询 响应类
 */
@XmlRootElement(name = "Reponse")
@XmlAccessorType(XmlAccessType.PROPERTY)
class QueryFeeItemInfoResult : BaseResult() {

    @get:XmlElement(name = "content")
    var contentList: MutableList<Content> = mutableListOf()

    class Content {

        /**
         * 项目名称
         */
        @get:XmlElement(name = "item_name")
        var itemName: String? = null

        /**
         * 价格
         */
        @get:XmlElement(name = "item_price")
        var itemPrice: String? = null

        /**
         * 类别
         */
        @get:XmlElement(name = "sys_class")
        var sysClass: String? = null

        /**
         * 等级
         */
        @get:XmlElement(name = "item_grade")
        var itemGrade: String? = null

    }

}
