package org.mospital.neusoft.result

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement


/**
 * 5.3.	预约支付
 */
@XmlRootElement(name = "reponse")
@XmlAccessorType(XmlAccessType.PROPERTY)
class FindQueueUpResult : BaseResult() {

        /**
         * 科室名称
         */
        @get:XmlElement(name = "departmentName")
        var departmentName: String = ""

        /**
         * 科室编码
         */
        @get:XmlElement(name = "departmentCode")
        var departmentCode: String = ""

        /**
         * 医生名称
         */
        @get:XmlElement(name = "doctorName")
        var doctorName: String = "普通医生"

        /**
         * 医生编码
         */
        @get:XmlElement(name = "doctorCode")
        var doctorCode: String = ""

        /**
         * 正在叫号
         */
        @get:XmlElement(name = "queueNownum")
        var queueNownum: String = ""

        /**
         * 排队号数
         */
        @get:XmlElement(name = "queueWaitnum")
        var queueWaitnum: String = ""

}