package org.mospital.neusoft.result

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

/**
 * 释放预约号源
 */
@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.PROPERTY)
class ReleaseSubscriptionResult: BaseResult() {

    @get:XmlElement(name = "scheduleId")
    var scheduleId: String = ""

    @get:XmlElement(name = "subscriptionId")
    var subscriptionId: String = ""

}