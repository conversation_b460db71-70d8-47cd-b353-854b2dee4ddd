package org.mospital.neusoft.result

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement


/**
 * 取消预约记录请求类
 */
@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.PROPERTY)
class CancelSubscriptionResult : BaseResult() {

    /**
     * His 退款流水号
     */
    @get:XmlElement(name = "hisRefundId")
    var hisRefundId: String? = null

}