package org.mospital.neusoft.result

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement


/**
 * 查询账单
 */
@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.PROPERTY)
class GetClinicBillResult : BaseResult() {


    @get:XmlElement(name = "content")
    var content: MutableList<Content> = mutableListOf()

    class Content {

        @get:XmlElement(name = "date")
        var date: String = ""

        @get:XmlElement(name = "type")
        var type: String = ""

        @get:XmlElement(name = "channel")
        var channel: String = ""

        @get:XmlElement(name = "hisNo")
        var hisNo: String = ""

        @get:XmlElement(name = "invoiceNo")
        var invoiceNo: String = ""

        @get:XmlElement(name = "amount")
        var amount: String = ""

        @get:XmlElement(name = "channelNo")
        var channelNo: String = ""

    }

}
