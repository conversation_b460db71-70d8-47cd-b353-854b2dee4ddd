package org.mospital.neusoft.result

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement


/**
 * 13.1.	交易总对账接口
 */
@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.PROPERTY)
class GetCheckLedgerInfoResult : BaseResult() {

    /**
     * 交易总金额
     */
    @get:XmlElement(name = "totalCash")
    var totalCash: String? = null

    /**
     * 交易总笔数
     */
    @get:XmlElement(name = "totalCount")
    var totalCount: String? = null

    /**
     * 退款总金额
     */
    @get:XmlElement(name = "refundCash")
    var refundCash: String? = null

    /**
     * 退款总笔数
     */
    @get:XmlElement(name = "refundCount")
    var refundCount: String? = null

}