package org.mospital.neusoft.result

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

/**
 * 查询门诊缴费列表（未交费）响应类
 */
@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.PROPERTY)
class FindOutpatientPayResult : BaseResult() {

    @get:XmlElement(name = "content")
    var content: MutableList<OutpatientPay> = mutableListOf()

    /**
     * 费用明细
     */
    class FeeDetail {
        /**
         * 处方编号
         */
        @get:XmlElement(name = "prescriptionNo")
        var prescriptionNo: String? = null

        /**
         * 创建时间
         */
        @get:XmlElement(name = "createDate")
        var createDate: String? = null

        /**
         * 就诊时间
         */
        @get:XmlElement(name = "patientTime")
        var patientTime: String? = null

        /**
         * 价格
         */
        @get:XmlElement(name = "price")
        var price: String? = null

        /**
         * 数量
         */
        @get:XmlElement(name = "count")
        var count: String? = null

        /**
         * 费用名称
         */
        @get:XmlElement(name = "feeName")
        var feeName: String? = null

        /**
         * 费用类型
         */
        @get:XmlElement(name = "feeType")
        var feeType: String? = null

        /**
         * 用法
         */
        @get:XmlElement(name = "usage")
        var usage: String? = null

        /**
         * 剂量
         */
        @get:XmlElement(name = "dosage")
        var dosage: String? = null
    }

    class OutpatientPay {
        /**
         * 门诊号
         */
        @get:XmlElement(name = "clinicNo")
        var clinicNo: String? = null

        /**
         * HIS 挂号 ID
         */
        @get:XmlElement(name = "hisRegisterId")
        var hisRegisterId: String? = null

        /**
         * 处方编号
         */
        @get:XmlElement(name = "prescriptionNo")
        var prescriptionNo: String? = null

        /**
         * 就诊人姓名
         */
        @get:XmlElement(name = "realName")
        var realName: String? = null

        /**
         * 就诊卡号
         */
        @get:XmlElement(name = "patientCard")
        var patientCard: String? = null

        /**
         * 订单金额
         */
        @get:XmlElement(name = "orderFee")
        var orderFee: String? = null

        /**
         * 订单创建时间
         */
        @get:XmlElement(name = "createDate")
        var createDate: String? = null

        /**
         * 费用类型
         */
        @get:XmlElement(name = "feeType")
        var feeType: String? = null

        /**
         * 科室名称
         */
        @get:XmlElement(name = "departmentName")
        var departmentName: String? = null

        /**
         * 科室编码
         */
        @get:XmlElement(name = "departmentCode")
        var departmentCode: String? = null

        /**
         * 医生姓名
         */
        @get:XmlElement(name = "doctorName")
        var doctorName: String? = null

        /**
         * 医生编码
         */
        @get:XmlElement(name = "doctorCode")
        var doctorCode: String? = null

        /**
         * 诊断编码
         */
        @get:XmlElement(name = "diagnosisCode")
        var diagnosisCode: String? = null

        /**
         * 诊断名称
         */
        @get:XmlElement(name = "diagnosisName")
        var diagnosisName: String? = null

        /**
         * 费用明细
         */
        @get:XmlElement(name = "feeDetailList")
        var feeDetailList: MutableList<FeeDetail> = mutableListOf()

    }

}