package org.mospital.neusoft.result

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement


/**
 * 医生信息查询接口响应类
 */
@XmlRootElement(name = "content")
@XmlAccessorType(XmlAccessType.PROPERTY)
class AddPatientCardInfo : BaseResult() {

    /**
     * 就诊卡号
     */
    @XmlElement(name = "patientCard")
    var patientCard: String = ""

    /**
     * HIS 患者唯一标识，病历号、登记号等
     */
    @XmlElement(name = "hisNo")
    var hisNo: String = ""

}
