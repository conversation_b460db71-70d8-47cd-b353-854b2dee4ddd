package org.mospital.neusoft.result

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement


/**
 * 查询账单
 */
@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.PROPERTY)
class GetClinicDayExpenseResult : BaseResult() {


    @get:XmlElement(name = "content")
    var content: MutableList<Content> = mutableListOf()

    class Content {

        @get:XmlElement(name = "operDate")
        var operDate: String = ""

        @get:XmlElement(name = "deptName")
        var deptName: String = ""

        @get:XmlElement(name = "clinicCode")
        var clinicCode: String = ""

        @get:XmlElement(name = "price")
        var price: String = ""

        @get:XmlElement(name = "count")
        var count: String = ""

        @get:XmlElement(name = "amount")
        var amount: String = ""

        @get:XmlElement(name = "feeType")
        var feeType: String = ""

    }

}
