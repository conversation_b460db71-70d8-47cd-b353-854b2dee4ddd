package org.mospital.neusoft.result

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement


@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.PROPERTY)
class OutpatientPayResult : BaseResult() {

    /** 发票号 */
    @get:XmlElement(name = "invoiceNo")
    var invoiceNo: String? = null

    /** HIS支付流水号 */
    @get:XmlElement(name = "hisRegisterId")
    var hisRegisterId: String? = null

}