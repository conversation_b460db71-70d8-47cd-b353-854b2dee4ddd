package org.mospital.neusoft.result

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement


/**
 * 3.4.	获取排班时间点
 */
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlRootElement(name = "response")
class QueryScheduleTimeResult : BaseResult() {

    @get:XmlElement(name = "content")
    var content: MutableList<ScheduleTime> = mutableListOf()

    class ScheduleTime {
        /**
         * 号源编号
         */
        @get:XmlElement(name = "sourceNo")
        var sourceNo: String = ""

        /**
         * 号源序号
         */
        @get:XmlElement(name = "sourceOrder")
        var sourceOrder: String = ""

        /**
         * 门诊日期. eg 2023/10/14 0:00:00
         */
        @get:XmlElement(name = "clinicDate")
        var clinicDate: String = ""

        /**
         * 就诊时间点
         */
        @get:XmlElement(name = "visitTime")
        var visitTime: String = ""

        /**
         * 是否有号
         */
        @get:XmlElement(name = "havaNo")
        var haveNo: String = ""
    }

}