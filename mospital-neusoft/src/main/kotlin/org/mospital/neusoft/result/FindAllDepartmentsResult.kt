package org.mospital.neusoft.result

import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement


/**
 * 科室信息查询接口响应类
 */
@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.PROPERTY)
class FindAllDepartmentsResult : BaseResult() {

    @get:XmlElement(name = "content")
    var content: MutableList<Department> = mutableListOf()

    class Department {
        /**
         * 科室名称
         */
        @get:XmlElement(name = "departmentName")
        var departmentName: String = ""

        /**
         * 科室编码（保持唯一）
         */
        @get:XmlElement(name = "departmentCode")
        var departmentCode: String = ""

        /**
         * 上级科室编码
         * 非必填
         */
        @get:XmlElement(name = "supperCode")
        var supperCode: String = ""

        /**
         * 科室级别(1:一级科室;2 或空为二级科室)
         * 非必填
         */
        @get:XmlElement(name = "departmentLevel")
        var departmentLevel: String = ""

        /**
         * 科室 Logo
         * 非必填
         */
        @get:XmlElement(name = "departmentLogo")
        var departmentLogo: String = ""

        /**
         * 科室地址(二级科室必填)
         * 非必填
         */
        @get:XmlElement(name = "departmentAddress")
        var departmentAddress: String = ""

        /**
         * 科室介绍(二级科室必填)
         * 非必填
         */
        @get:XmlElement(name = "departmentSummary")
        var departmentSummary: String = ""

        /**
         * 科室特色(二级科室必填)
         * 非必填
         */
        @get:XmlElement(name = "departmentFeature")
        var departmentFeature: String = ""
    }
}