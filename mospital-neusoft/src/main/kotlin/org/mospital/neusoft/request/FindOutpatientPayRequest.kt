package org.mospital.neusoft.request

import org.mospital.neusoft.result.FindOutpatientPayResult
import jakarta.xml.bind.annotation.*

/**
 * 查询门诊缴费列表（未交费）请求类
 */
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlRootElement(name = "request")
@XmlType(propOrder = ["realName", "patientCard", "clinicNo"])
class FindOutpatientPayRequest : BaseRequest<FindOutpatientPayResult>() {

    /**
     * 就诊人姓名
     */
    @get:XmlElement(name = "realName")
    var realName: String? = null

    /**
     * 就诊卡号
     */
    @get:XmlElement(name = "patientCard")
    var patientCard: String? = null

    /**
     * 门诊号
     */
    @get:XmlElement(name = "clinicNo")
    var clinicNo: String? = null

    override val method: String
        get() = "findOutpatientPay"
    override val responseClass: Class<FindOutpatientPayResult>
        get() = FindOutpatientPayResult::class.java

}