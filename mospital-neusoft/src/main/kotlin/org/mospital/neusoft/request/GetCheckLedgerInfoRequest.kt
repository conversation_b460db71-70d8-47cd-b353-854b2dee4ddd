package org.mospital.neusoft.request

import org.mospital.neusoft.result.GetCheckLedgerInfoResult
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

/**
 * 13.1.	交易总对账接口
 */
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.PROPERTY)
class GetCheckLedgerInfoRequest : BaseRequest<GetCheckLedgerInfoResult>() {

    /**
     * 对账日期,请his按照支付接口入参中的支付成功时间payedTime筛选数据
     */
    @get:XmlElement(name = "checkDate")
    var checkDate: String = ""

    @get:XmlElement(name = "transactionType")
    var transactionType: String = "2"

    override val method: String
        get() = "GetCheckLedgerDetail"
    override val responseClass: Class<GetCheckLedgerInfoResult>
        get() = GetCheckLedgerInfoResult::class.java

}