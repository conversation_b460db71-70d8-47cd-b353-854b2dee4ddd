package org.mospital.neusoft.request

import org.mospital.neusoft.result.ReleaseSubscriptionResult
import jakarta.xml.bind.annotation.*

/**
 * 释放预约号源
 */
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(propOrder = ["scheduleId", "subscriptionId"])
class ReleaseSubscriptionRequest : BaseRequest<ReleaseSubscriptionResult>() {

    @get:XmlElement(name = "scheduleId")
    var scheduleId: String = ""

    @get:XmlElement(name = "subscriptionId")
    var subscriptionId: String = ""

    override val method: String
        get() = "releaseSubscription"

    override val responseClass: Class<ReleaseSubscriptionResult>
        get() = ReleaseSubscriptionResult::class.java
}