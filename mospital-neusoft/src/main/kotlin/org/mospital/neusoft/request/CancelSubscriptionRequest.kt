package org.mospital.neusoft.request

import org.mospital.neusoft.result.CancelSubscriptionResult
import jakarta.xml.bind.annotation.*

/**
 * 取消预约记录请求类
 */
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlRootElement(name = "request")
@XmlType(propOrder = ["subscriptionId","tradeNo","tradeDate"])
class CancelSubscriptionRequest : BaseRequest<CancelSubscriptionResult>() {
    /**
     * 预约记录主键
     */
    @get:XmlElement(name = "subscriptionId")
    var subscriptionId: String = ""

    /**
     * 交易单号
     */
    @get:XmlElement(name = "tradeNo")
    var tradeNo: String = ""

    /**
     * 退号时间
     */
    @get:XmlElement(name = "tradeDate")
    var tradeDate: String = ""

    override val method: String
        get() = "cancelSubscription"
    override val responseClass: Class<CancelSubscriptionResult>
        get() = CancelSubscriptionResult::class.java

}