package org.mospital.neusoft.request

import org.mospital.neusoft.result.SubscriptionHistoryResult
import jakarta.xml.bind.annotation.*

/**
 * 查询门诊缴费列表（未交费）请求类
 */
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlRootElement(name = "request")
@XmlType(propOrder = ["patientCard", "startDate", "endDate"])
class SubscriptionHistoryRequest : BaseRequest<SubscriptionHistoryResult>() {

    /**
     * 就诊人卡号
     */
    @get:XmlElement(name = "patientCard")
    var patientCard: String? = null

    /**
     * 开始日期
     */
    @get:XmlElement(name = "startDate")
    var startDate: String? = null

    /**
     * 结束日期
     */
    @get:XmlElement(name = "endDate")
    var endDate: String? = null

    override val method: String
        get() = "subscriptionHistory"
    override val responseClass: Class<SubscriptionHistoryResult>
        get() = SubscriptionHistoryResult::class.java

}