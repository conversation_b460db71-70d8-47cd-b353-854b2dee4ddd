package org.mospital.neusoft.request

import org.mospital.neusoft.result.QueryInHospitalPatientResult
import jakarta.xml.bind.annotation.*

/**
 * 4.5.	住院病人历史查询
 */
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(propOrder = ["realName", "idCard", "serialNumber"])
class QueryInHospitalListRequest : BaseRequest<QueryInHospitalPatientResult>() {

    /**
     * 姓名
     */
    @get:XmlElement(name = "realName")
    var realName: String? = null

    /**
     * 身份证号
     */
    @get:XmlElement(name = "idCard")
    var idCard: String = ""

    /**
     * 住院号
     */
    @get:XmlElement(name = "serialNumber")
    var serialNumber: String = ""

    override val method: String
        get() = "queryInHospitalList"
    override val responseClass: Class<QueryInHospitalPatientResult>
        get() = QueryInHospitalPatientResult::class.java

}