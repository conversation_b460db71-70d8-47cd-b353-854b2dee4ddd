package org.mospital.neusoft.request

import org.mospital.neusoft.result.GetClinicBillResult
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlRootElement
import jakarta.xml.bind.annotation.XmlType

/**
 * 查询账单
 */
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(propOrder = ["realName", "queryId", "startDate", "endDate", "type"])
class GetClinicBillRequest : BaseRequest<GetClinicBillResult>() {

    /**
     * 就诊人姓名
     */
    var realName: String = ""

    /**
     * 就诊卡号/住院号
     */
    var queryId: String = ""

    /**
     * 排班起始日期,格式yyyy-MM-dd
     */
    var startDate: String = ""

    /**
     * 排班结束日期,格式yyyy-MM-dd，不传则默认7天
     */
    var endDate: String = ""

    /**
     * 类型:1=门诊，2=住院
     */
    var type: String = ""

    override val method: String
        get() = "getClinicBill"
    override val responseClass: Class<GetClinicBillResult>
        get() = GetClinicBillResult::class.java

}