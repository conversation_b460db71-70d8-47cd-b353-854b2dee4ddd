package org.mospital.neusoft.request

import org.mospital.neusoft.result.FindAllDepartmentsResult
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlRootElement

/**
 * 2.1.	获取科室列表信息
 */
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.PROPERTY)
class FindAllDepartmentsRequest : BaseRequest<FindAllDepartmentsResult>() {

    override val method: String
        get() = "findAllDepartments"
    override val responseClass: Class<FindAllDepartmentsResult>
        get() = FindAllDepartmentsResult::class.java

}