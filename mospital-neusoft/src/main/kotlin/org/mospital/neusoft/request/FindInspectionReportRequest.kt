package org.mospital.neusoft.request

import org.mospital.neusoft.result.FindInspectionReportResult
import jakarta.xml.bind.annotation.*

/**
 * 7.1.	查询检验、检查报告列表(默认查询一个月内的报告)
 */
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(propOrder = ["queryId", "startDate", "endDate", "reportType"])
class FindInspectionReportRequest : BaseRequest<FindInspectionReportResult>() {

    /**
     * 查询 ID
     */
    @get:XmlElement(name = "queryId")
    var queryId: String = ""

    /**
     * 查询起始日期
     */
    @get:XmlElement(name = "startDate")
    var startDate: String? = null

    /**
     * 查询截至日期
     */
    @get:XmlElement(name = "endDate")
    var endDate: String? = null

    /**
     * 报告类型
     * 检查报告类型 1：门诊 2：住院
     */
    @get:XmlElement(name = "reportType")
    var reportType: String = ""

    override val method: String
        get() = "findInspectionReport"

    override val responseClass: Class<FindInspectionReportResult>
        get() = FindInspectionReportResult::class.java

}