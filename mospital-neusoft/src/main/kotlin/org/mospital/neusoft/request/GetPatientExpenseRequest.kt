package org.mospital.neusoft.request

import org.mospital.neusoft.result.GetPatientExpenseResult
import jakarta.xml.bind.annotation.*

/**
 * 9.1.	获取就诊人已预缴的住院费用
 */
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(propOrder = ["realName", "queryId", "date"])
class GetPatientExpenseRequest : BaseRequest<GetPatientExpenseResult>() {

    /**
     * 就诊人姓名
     */
    @get:XmlElement(name = "realName")
    var realName: String? = null

    /**
     * 查询ID
     */
    @get:XmlElement(name = "queryId")
    var queryId: String? = null

    /**
     * 查询日期
     */
    @get:XmlElement(name = "date")
    var date: String? = null

    override val method: String
        get() = "getPatientExpense"
    override val responseClass: Class<GetPatientExpenseResult>
        get() = GetPatientExpenseResult::class.java

}