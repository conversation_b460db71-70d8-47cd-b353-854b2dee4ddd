package org.mospital.neusoft.request

import org.mospital.neusoft.result.CheckPatientCardResult
import jakarta.xml.bind.annotation.*

/**
 * 4.2.	就诊人信息校验
 */
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(propOrder = ["patientCard", "realName", "idCard", "phone"])
class CheckPatientCardRequest : BaseRequest<CheckPatientCardResult>() {

    /**
     * 就诊人卡号
     */
    @get:XmlElement(name = "patientCard")
    var patientCard: String? = null

    /**
     * 就诊人姓名
     */
    @get:XmlElement(name = "realName")
    var realName: String? = null

    /**
     * 就诊人身份证号
     */
    @get:XmlElement(name = "idCard")
    var idCard: String? = null

    /**
     * 联系电话
     */
    @get:XmlElement(name = "phone")
    var phone: String? = null

    override val method: String
        get() = "checkPatientCard"
    override val responseClass: Class<CheckPatientCardResult>
        get() = CheckPatientCardResult::class.java

}