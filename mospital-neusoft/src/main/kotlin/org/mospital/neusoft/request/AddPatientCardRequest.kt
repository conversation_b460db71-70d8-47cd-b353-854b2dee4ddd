package org.mospital.neusoft.request

import org.mospital.neusoft.result.AddPatientCardResult
import org.mospital.neusoft.util.IdCardType
import jakarta.xml.bind.annotation.*

/**
 * 4.1.	在线建卡
 */
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(propOrder = ["realName", "phone", "idCard", "country", "nation", "sex", "address", "erhcCardInfo128", "birthDay", "babyFlag", "jhrId", "jhrName", "idCardType"])
class AddPatientCardRequest : BaseRequest<AddPatientCardResult>() {

    /**
     * 就诊人姓名
     */
    @get:XmlElement(name = "realName")
    var realName: String = ""

    /**
     * 证件号码
     */
    @get:XmlElement(name = "idCard")
    var idCard: String = ""

    /**
     * 证件类型
     */
    @get:XmlElement(name = "idCardType")
    var idCardType: String = IdCardType.SHEN_FEN_ZHENG.code

    /**
     * 联系电话
     */
    @get:XmlElement(name = "phone")
    var phone: String = ""

    /**
     * 联系电话
     */
    @get:XmlElement(name = "sex")
    var sex: String = ""

    /**
     * 住址
     */
    @get:XmlElement(name = "address")
    var address: String = ""

    /**
     * 国籍
     */
    @get:XmlElement(name = "country")
    var country: String = "cn"

    /**
     * 民族
     */
    @get:XmlElement(name = "nation")
    var nation: String = ""

    /**
     * 电子健康卡号
     */
    @get:XmlElement(name = "EhealCardInfo128")
    var erhcCardInfo128: String = ""

    /**
     * 生日
     */
    @get:XmlElement(name = "birthDay")
    var birthDay: String = ""

    /**
     * 是否新生儿 0： 本人  1：新生儿童
     */
    @get:XmlElement(name = "babyFlag")
    var babyFlag: String = "0"

    /**
     * 监护人证件号
     */
    @get:XmlElement(name = "jhrId")
    var jhrId: String = ""


    /**
     * 监护人姓名
     */
    @get:XmlElement(name = "jhrName")
    var jhrName: String = ""

    override val method: String
        get() = "addPatientCard"
    override val responseClass: Class<AddPatientCardResult>
        get() = AddPatientCardResult::class.java

}