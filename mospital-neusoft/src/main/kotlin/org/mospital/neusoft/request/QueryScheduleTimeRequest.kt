package org.mospital.neusoft.request

import org.mospital.neusoft.result.QueryScheduleTimeResult
import jakarta.xml.bind.annotation.*

/**
 * 3.4.	获取排班时间点
 */
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(propOrder = ["departmentCode", "clinicDate", "clinicTime", "doctorCode", "scheduleId"])
class QueryScheduleTimeRequest : BaseRequest<QueryScheduleTimeResult>() {

    /**
     * 科室代码
     */
    @get:XmlElement(name = "departmentCode")
    var departmentCode: String? = null

    /**
     * 就诊日期
     */
    @get:XmlElement(name = "clinicDate")
    var clinicDate: String? = null

    /**
     * 就诊时间：上午/下午
     */
    @get:XmlElement(name = "clinicTime")
    var clinicTime: String? = null

    /**
     * 医生代码，如果为以上排班，则必输
     */
    @get:XmlElement(name = "doctorCode")
    var doctorCode: String? = null

    /**
     * 排班主键，根据 HIS 系统决定是否必输
     */
    @get:XmlElement(name = "scheduleId")
    var scheduleId: String? = null

    override val method: String
        get() = "queryScheduleTime"
    override val responseClass: Class<QueryScheduleTimeResult>
        get() = QueryScheduleTimeResult::class.java

}