package org.mospital.neusoft.request

import org.mospital.neusoft.result.QueryRealtimeScheduleResult
import jakarta.xml.bind.annotation.*

/**
 * 3.2.	医生坐诊信息查询（实时排班使用）
 */
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(propOrder = ["startDate", "endDate", "departmentCode"])
class QueryRealtimeScheduleRequest : BaseRequest<QueryRealtimeScheduleResult>() {

    /**
     * 排班起始日期，格式 yyyy-MM-dd
     */
    @get:XmlElement(name = "startDate")
    var startDate: String? = null

    /**
     * 排班结束日期，格式 yyyy-MM-dd，不传则默认 7 天
     */
    @get:XmlElement(name = "endDate")
    var endDate: String? = null

    /**
     * 科室代码
     */
    @get:XmlElement(name = "departmentCode")
    var departmentCode: String? = null

    override val method: String
        get() = "queryRealtimeSchedule"
    override val responseClass: Class<QueryRealtimeScheduleResult>
        get() = QueryRealtimeScheduleResult::class.java

}