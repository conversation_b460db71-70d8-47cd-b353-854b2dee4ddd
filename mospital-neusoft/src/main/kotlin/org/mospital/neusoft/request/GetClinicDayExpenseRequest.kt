package org.mospital.neusoft.request

import org.mospital.neusoft.result.GetClinicDayExpenseResult
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlRootElement
import jakarta.xml.bind.annotation.XmlType

/**
 * 查询门诊费用清单
 */
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(propOrder = ["realName", "patientCard", "startDate", "endDate"])
class GetClinicDayExpenseRequest : BaseRequest<GetClinicDayExpenseResult>() {

    /**
     * 就诊人姓名
     */
    var realName: String = ""

    /**
     * 就诊卡号/住院号
     */
    var patientCard: String = ""

    /**
     * 排班起始日期,格式yyyy-MM-dd
     */
    var startDate: String = ""

    /**
     * 排班结束日期,格式yyyy-MM-dd，不传则默认7天
     */
    var endDate: String = ""

    override val method: String
        get() = "getClinicDayExpense"
    override val responseClass: Class<GetClinicDayExpenseResult>
        get() = GetClinicDayExpenseResult::class.java

}