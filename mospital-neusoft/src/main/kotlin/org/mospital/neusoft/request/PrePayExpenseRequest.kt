package org.mospital.neusoft.request

import org.mospital.neusoft.result.PrePayExpenseResult
import jakarta.xml.bind.annotation.*

/**
 * 10.1.	预缴住院费用
 */
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(propOrder = ["realName", "patientCard", "prepayaAmt", "payType", "tradeNo", "payedTime"])
class PrePayExpenseRequest : BaseRequest<PrePayExpenseResult>() {

    /**
     * 患者姓名
     */
    @get:XmlElement(name = "realName")
    var realName: String? = null

    /**
     * 住院号/就诊卡号
     */
    @get:XmlElement(name = "patientCard")
    var patientCard: String? = null

    /**
     * 预缴金额
     */
    @get:XmlElement(name = "prepayaAmt")
    var prepayaAmt: String? = null

    /**
     * 付款来源（weChat/APP/aliPay/eCardPay）
     */
    @get:XmlElement(name = "payType")
    var payType: String? = "weChat"

    /**
     * 商户交易订单号（校验唯一性）
     */
    @get:XmlElement(name = "tradeNo")
    var tradeNo: String? = null

    /**
     * 支付成功时间，用于对账
     */
    @get:XmlElement(name = "payedTime")
    var payedTime: String? = null

    override val method: String
        get() = "prePayExpense"

    override val responseClass: Class<PrePayExpenseResult>
        get() = PrePayExpenseResult::class.java

}