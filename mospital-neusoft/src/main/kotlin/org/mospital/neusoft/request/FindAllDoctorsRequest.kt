package org.mospital.neusoft.request

import org.mospital.neusoft.result.FindAllDoctorsResult
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlRootElement

/**
 * 2.2.	获取医生列表信息
 */
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.PROPERTY)
class FindAllDoctorsRequest : BaseRequest<FindAllDoctorsResult>() {

    override val method: String
        get() = "findAllDoctors"
    override val responseClass: Class<FindAllDoctorsResult>
        get() = FindAllDoctorsResult::class.java

}