package org.mospital.neusoft.request

import org.mospital.neusoft.result.QueryScheduleResult
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlRootElement
import jakarta.xml.bind.annotation.XmlType

/**
 * 3.1.	获取排班
 */
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(propOrder = ["startDate", "endDate"])
class QueryScheduleRequest : BaseRequest<QueryScheduleResult>() {

    /**
     * 排班起始日期,格式yyyy-MM-dd
     */
    var startDate: String = ""

    /**
     * 排班结束日期,格式yyyy-MM-dd，不传则默认7天
     */
    var endDate: String = ""

    override val method: String
        get() = "querySchedule"
    override val responseClass: Class<QueryScheduleResult>
        get() = QueryScheduleResult::class.java

}