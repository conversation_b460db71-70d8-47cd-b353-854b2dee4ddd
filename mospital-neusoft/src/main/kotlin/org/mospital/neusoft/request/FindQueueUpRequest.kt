package org.mospital.neusoft.request

import org.mospital.neusoft.result.FindQueueUpResult
import jakarta.xml.bind.annotation.*

/**
 * 查询门诊缴费列表（未交费）请求类
 */
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlRootElement(name = "request")
@XmlType(propOrder = ["departmentName", "departmentCode"])
class FindQueueUpRequest : BaseRequest<FindQueueUpResult>() {

    /**
     * 科室名称
     */
    @get:XmlElement(name = "departmentName")
    var departmentName: String? = null

    /**
     * 科室编码
     */
    @get:XmlElement(name = "departmentCode")
    var departmentCode: String? = null

    override val method: String
        get() = "findQueueUp"
    override val responseClass: Class<FindQueueUpResult>
        get() = FindQueueUpResult::class.java

}