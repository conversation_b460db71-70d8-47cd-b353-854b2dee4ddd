package org.mospital.neusoft.request

import org.mospital.neusoft.result.QueryPatientResult
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlRootElement

/**
 * 4.3.	门诊病人信息查询
 */
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.PROPERTY)
class QueryPatientRequest(
    var patientCard: String = ""
) : BaseRequest<QueryPatientResult>() {

    override val method: String
        get() = "queryPatientInfo"
    override val responseClass: Class<QueryPatientResult>
        get() = QueryPatientResult::class.java

}