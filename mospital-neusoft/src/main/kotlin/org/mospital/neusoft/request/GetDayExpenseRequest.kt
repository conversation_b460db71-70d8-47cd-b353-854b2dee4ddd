package org.mospital.neusoft.request

import org.mospital.neusoft.result.GetDayExpenseResult
import jakarta.xml.bind.annotation.*

/**
 * 9.2.	获取某日住院费用清单 请求类
 */
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(propOrder = ["realName", "queryId", "date"])
class GetDayExpenseRequest : BaseRequest<GetDayExpenseResult>() {

    /**
     * 就诊人姓名
     */
    @get:XmlElement(name = "realName")
    var realName: String? = null

    /**
     * 就诊人卡号
     */
    @get:XmlElement(name = "queryId")
    var queryId: String? = null

    /**
     * 就诊人卡号
     */
    @get:XmlElement(name = "date")
    var date: String? = null

    override val method: String
        get() = "getDayExpense"
    override val responseClass: Class<GetDayExpenseResult>
        get() = GetDayExpenseResult::class.java

}