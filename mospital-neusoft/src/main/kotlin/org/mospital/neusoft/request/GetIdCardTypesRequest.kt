package org.mospital.neusoft.request

import org.mospital.neusoft.result.GetIdCardTypesResult
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlRootElement

@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.PROPERTY)
class GetIdCardTypesRequest : BaseRequest<GetIdCardTypesResult>() {

    override val method: String
        get() = "getIdCardTypes"
    override val responseClass: Class<GetIdCardTypesResult>
        get() = GetIdCardTypesResult::class.java

}