package org.mospital.neusoft.request

import org.mospital.neusoft.result.FindCompleteLISReportResult
import jakarta.xml.bind.annotation.*

/**
 * 7.2.	查询检验报告详情
 */
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(propOrder = ["reportId", "checkType"])
class FindCompleteLISReportRequest : BaseRequest<FindCompleteLISReportResult>() {

    /**
     * 接口 7.1 返回
     */
    @get:XmlElement(name = "reportId")
    var reportId: String = ""

    /**
     * 1-检验 2-检查
     */
    @get:XmlElement(name = "checkType")
    var checkType: String = ""

    override val method: String
        get() = "findCompleteLISReport"
    override val responseClass: Class<FindCompleteLISReportResult>
        get() = FindCompleteLISReportResult::class.java

}