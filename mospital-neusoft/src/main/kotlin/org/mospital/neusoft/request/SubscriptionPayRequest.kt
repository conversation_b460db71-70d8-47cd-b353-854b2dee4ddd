package org.mospital.neusoft.request

import org.mospital.neusoft.result.SubscriptionPayResult
import jakarta.xml.bind.annotation.*

/**
 * 5.3.	预约支付
 */
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(propOrder = ["scheduleId", "subscriptionId", "tradeNo", "realName", "idCard", "patientCard", "payType", "departmentName", "departmentCode", "doctorName", "doctorCode", "clinicDate", "clinicTimeQuantum", "clinicFee", "payedTime"])
class SubscriptionPayRequest : BaseRequest<SubscriptionPayResult>() {

    /**
     * 排班编号
     */
    @get:XmlElement(name = "scheduleId")
    var scheduleId: String = ""

    /**
     * 预约记录主键
     */
    @get:XmlElement(name = "subscriptionId")
    var subscriptionId: String = ""

    /**
     * 商户交易订单号
     */
    @get:XmlElement(name = "tradeNo")
    var tradeNo: String = ""

    /**
     * 就诊人真实姓名
     */
    @get:XmlElement(name = "realName")
    var realName: String = ""

    /**
     * 就诊人身份证
     */
    @get:XmlElement(name = "idCard")
    var idCard: String = ""

    /**
     * 就诊人卡号
     */
    @get:XmlElement(name = "patientCard")
    var patientCard: String? = null

    /**
     * 支付方式
     */
    @get:XmlElement(name = "payType")
    var payType: String? = null

    /**
     * 挂号科室名称
     */
    @get:XmlElement(name = "departmentName")
    var departmentName: String? = null

    /**
     * 挂号科室编号
     */
    @get:XmlElement(name = "departmentCode")
    var departmentCode: String = ""

    /**
     * 医生名称
     */
    @get:XmlElement(name = "doctorName")
    var doctorName: String? = null

    /**
     * 医生编号
     */
    @get:XmlElement(name = "doctorCode")
    var doctorCode: String = ""

    /**
     * 就诊时间
     */
    @get:XmlElement(name = "clinicDate")
    var clinicDate: String = ""

    /**
     * 就诊时间段
     */
    @get:XmlElement(name = "clinicTimeQuantum")
    var clinicTimeQuantum: String = ""

    /**
     * 预约金额
     */
    @get:XmlElement(name = "clinicFee")
    var clinicFee: Double = 0.0

    /**
     * 支付成功时间
     */
    @get:XmlElement(name = "payedTime")
    var payedTime: String = ""

    override val method: String
        get() = "subscriptionPay"
    override val responseClass: Class<SubscriptionPayResult>
        get() = SubscriptionPayResult::class.java

}