package org.mospital.neusoft.request

import org.mospital.neusoft.result.QueryFeeItemInfoResult
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlRootElement

/**
 * 物价查询
 */
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.PROPERTY)
class QueryFeeItemInfoRequest() : BaseRequest<QueryFeeItemInfoResult>() {

    override val method: String
        get() = "QueryFeeItemInfo"
    override val responseClass: Class<QueryFeeItemInfoResult>
        get() = QueryFeeItemInfoResult::class.java

}