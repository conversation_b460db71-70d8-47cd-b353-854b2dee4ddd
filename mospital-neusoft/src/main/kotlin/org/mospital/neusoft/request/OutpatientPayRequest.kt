package org.mospital.neusoft.request

import org.mospital.neusoft.result.OutpatientPayResult
import jakarta.xml.bind.annotation.*

/**
 * 诊间支付
 */
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(propOrder = ["prescriptionNo", "realName", "patientCard", "orderFee", "payType", "feeType", "payedTime", "tradeNo"])
class OutpatientPayRequest : BaseRequest<OutpatientPayResult>() {
    /** 费用单编号，多个处方用,分隔 */
    @get:XmlElement(name = "prescriptionNo")
    var prescriptionNo: String? = null

    /** 就诊人姓名 */
    @get:XmlElement(name = "realName")
    var realName: String? = null

    /** 就诊人卡号 */
    @get:XmlElement(name = "patientCard")
    var patientCard: String? = null

    /** 缴费金额 */
    @get:XmlElement(name = "orderFee")
    var orderFee: String? = null

    /** 付款来源（weChat/APP/aliPay/eCardPay） */
    @get:XmlElement(name = "payType")
    var payType: String? = null

    /** 费用类型 */
    @get:XmlElement(name = "feeType")
    var feeType: String? = null

    /** 支付成功时间，用于对账 */
    @get:XmlElement(name = "payedTime")
    var payedTime: String? = null

    /** 商户交易订单号（校验唯一性） */
    @get:XmlElement(name = "tradeNo")
    var tradeNo: String? = null

    override val method: String
        get() = "outpatientPay"
    override val responseClass: Class<OutpatientPayResult>
        get() = OutpatientPayResult::class.java

}