package org.mospital.neusoft.request

import org.mospital.neusoft.result.InsertSubscriptionResult
import jakarta.xml.bind.annotation.*

/**
 * 5.1.	新增预约记录
 */
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(propOrder = ["scheduleId", "sourceNo", "sourceOrder", "realName", "idCard", "patientCard", "departmentName", "departmentCode", "doctorName", "doctorCode", "clinicType", "clinicDate", "clinicTime", "clinicFee","payType","tradeNo","tradeDate"])
class InsertSubscriptionRequest : BaseRequest<InsertSubscriptionResult>() {

    /**
     * 排班编号
     */
    @get:XmlElement(name = "scheduleId")
    var scheduleId: String? = null

    /**
     * 号源编号
     */
    @get:XmlElement(name = "sourceNo")
    var sourceNo: String? = null

    /**
     * 号源顺序
     */
    @get:XmlElement(name = "sourceOrder")
    var sourceOrder: Int? = null

    /**
     * 就诊人真实姓名
     */
    @get:XmlElement(name = "realName")
    var realName: String? = null

    /**
     * 就诊人身份证号
     */
    @get:XmlElement(name = "idCard")
    var idCard: String? = null

    /**
     * 就诊人卡号
     */
    @get:XmlElement(name = "patientCard")
    var patientCard: String? = null

    /**
     * 挂号科室名称
     */
    @get:XmlElement(name = "departmentName")
    var departmentName: String? = null

    /**
     * 挂号科室编号
     */
    @get:XmlElement(name = "departmentCode")
    var departmentCode: String? = null

    /**
     * 医生名称
     */
    @get:XmlElement(name = "doctorName")
    var doctorName: String? = null

    /**
     * 医生编号
     */
    @get:XmlElement(name = "doctorCode")
    var doctorCode: String? = null

    /**
     * 门诊类型
     */
    @get:XmlElement(name = "clinicType")
    var clinicType: String? = null

    /**
     * 预约日期
     */
    @get:XmlElement(name = "clinicDate")
    var clinicDate: String? = null

    /**
     * 预约时间段
     */
    @get:XmlElement(name = "clinicTime")
    var clinicTime: String? = null

    /**
     * 挂号费用（单位:元）
     */
    @get:XmlElement(name = "clinicFee")
    var clinicFee: String? = null

    /**
     * 支付来源
     */
    @get:XmlElement(name = "payType")
    var payType: String? = null

    /**
     * 交易日期
     */
    @get:XmlElement(name = "tradeDate")
    var tradeDate: String? = null

    /**
     * 订单号
     */
    @get:XmlElement(name = "tradeNo")
    var tradeNo: String? = null

    override val method: String
        get() = "insertSubscription"
    override val responseClass: Class<InsertSubscriptionResult>
        get() = InsertSubscriptionResult::class.java

}