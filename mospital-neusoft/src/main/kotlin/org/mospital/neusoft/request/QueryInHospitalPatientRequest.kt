package org.mospital.neusoft.request

import org.mospital.neusoft.result.QueryInHospitalPatientResult
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlElement
import jakarta.xml.bind.annotation.XmlRootElement

/**
 * 4.4.	住院病人信息查询
 */
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.PROPERTY)
class QueryInHospitalPatientRequest : BaseRequest<QueryInHospitalPatientResult>() {

    /**
     * 住院号
     */
    @get:XmlElement(name = "serialNumber")
    var serialNumber: String? = null

    override val method: String
        get() = "queryInHospitalPatient"
    override val responseClass: Class<QueryInHospitalPatientResult>
        get() = QueryInHospitalPatientResult::class.java

}