package org.mospital.neusoft.request

import org.mospital.neusoft.result.RealtimeScheduleDetailInfoResult
import jakarta.xml.bind.annotation.*

/**
 * 2.1.	获取科室列表信息
 */
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.PROPERTY)
@XmlType(propOrder = ["clinicDate", "departmentCode", "doctorCode"])
class QueryRealtimeScheduleDetailRequest : BaseRequest<RealtimeScheduleDetailInfoResult>() {

    /**
     * 科室代码
     */
    @get:XmlElement(name = "departmentCode")
    var departmentCode: String? = null

    /**
     * 就诊日期，格式 yyyy-MM-dd
     */
    @get:XmlElement(name = "clinicDate")
    var clinicDate: String? = null

    /**
     * 医生代码
     */
    @get:XmlElement(name = "doctorCode")
    var doctorCode: String? = null

    override val method: String
        get() = "queryRealtimeScheduleDetail"
    override val responseClass: Class<RealtimeScheduleDetailInfoResult>
        get() = RealtimeScheduleDetailInfoResult::class.java

}