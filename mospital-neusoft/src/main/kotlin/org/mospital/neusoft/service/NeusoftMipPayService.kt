package org.mospital.neusoft.service

import com.fasterxml.jackson.core.JacksonException
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.kotlinModule
import kotlinx.coroutines.runBlocking
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody
import okhttp3.ResponseBody
import okhttp3.ResponseBody.Companion.toResponseBody
import org.mospital.common.http.HttpClientFactory
import org.mospital.neusoft.mip.*
import org.slf4j.LoggerFactory
import retrofit2.Converter
import retrofit2.Retrofit
import retrofit2.converter.jackson.JacksonConverterFactory
import java.lang.reflect.Type

@Suppress("unused")
object NeusoftMipPayService {

    private val logger = LoggerFactory.getLogger(NeusoftMipPayService::class.java)
    private val serviceCache = mutableMapOf<String, HisMipStanderService>()

    private val MEDIA_TYPE_JSON = "application/json; charset=utf-8".toMediaType()

    private fun createHisMipService(baseUrl: String): HisMipStanderService {
        val httpClient = HttpClientFactory.createWithRequestIdLogging(
            logger = logger,
            connectTimeout = 10_000,
            readTimeout = 30_000,
            useUuidRequestId = true
        ) {
            it.retryOnConnectionFailure(false)
            it.addInterceptor(ErrorInterceptor())
            it.addInterceptor(HisMipSoapRequestConvertInterceptor())
        }

        val retrofit = Retrofit.Builder()
            .baseUrl(baseUrl)
            .client(httpClient)
            .addConverterFactory(object : Converter.Factory() {
                // 这里我们使用 JacksonConverterFactory 进行转换 同时统一配置@JsonIgnoreProperties(ignoreUnknown = true)
                val delegate = JacksonConverterFactory.create(ObjectMapper().apply {
                    configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                    kotlinModule()
                })

                override fun requestBodyConverter(
                    type: Type,
                    parameterAnnotations: Array<out Annotation>,
                    methodAnnotations: Array<out Annotation>,
                    retrofit: Retrofit
                ): Converter<*, RequestBody>? {
                    return delegate.requestBodyConverter(type, parameterAnnotations, methodAnnotations, retrofit)
                }

                override fun responseBodyConverter(
                    type: Type,
                    annotations: Array<out Annotation>,
                    retrofit: Retrofit
                ): Converter<ResponseBody, *> {
                    return Converter<ResponseBody, Any> { value ->
                        try {
                            val jsonResponse = parseJsonFromSoapResponse(value.string())
                            val newResponseBody = jsonResponse.toResponseBody(MEDIA_TYPE_JSON)
                            // 这里我们使用 JacksonConverterFactory 进行转换
                            delegate.responseBodyConverter(type, annotations, retrofit)!!.convert(newResponseBody)
                        } catch (e: JacksonException) {
                            logger.error("His医保接口异常:$e")
                            HisMipBaseResult<String>().apply {
                                this.code = 500
                                this.message = "his医保接口反序列化异常"
                            }

                        } finally {
                            value.close()
                        }
                    }
                }
            })
            .build()
        return retrofit.create(HisMipStanderService::class.java)
    }

    @Synchronized
    private fun getService(env: String = NeusoftMipConfigs.DEFAULT_ENV): HisMipStanderService {
        val baseUrl = NeusoftMipConfigs.INSTANCE.getConfigByEnv(env).url
        return serviceCache.getOrPut(baseUrl) { createHisMipService(baseUrl) }
    }

    private fun parseJsonFromSoapResponse(soapResponse: String): String {
        return try {
            val start = soapResponse.indexOf("<CallServiceResult>") + "<CallServiceResult>".length
            val end = soapResponse.indexOf("</CallServiceResult>")
            soapResponse.substring(start, end)
        } catch (e: Exception) {
            e.printStackTrace()
            "{\"Code\":-1,\"AppCode\":\"元启图灵\",\"Message\":\"his医保回参异常\",\"OutData\":null,\"ExtParam\":null}"
        }
    }


    /**
     * his 上传明细并下单接口
     */
    fun mipSettle(
        request: HisMipBaseRequest<MipSettleRequest>,
        env: String = NeusoftMipConfigs.DEFAULT_ENV
    ): HisMipBaseResult<MipSettleResponse> =
        runBlocking {
            getService(env).mipSettle(request.apply {
                this.funCode = "M001"
            })
        }


    /**
     * his 医保门诊处方结算回调
     */
    fun settleCallBack(
        request: HisMipBaseRequest<MipSettleCallBackRequest>,
        env: String = NeusoftMipConfigs.DEFAULT_ENV
    ): HisMipBaseResult<MipSettleCallBackResult> =
        runBlocking {
            getService(env).settleCallBack(request.apply {
                this.funCode = "M002"
            })
        }


    /**
     * his 医保订单状态查询
     */
    fun settleStatusQuery(
        request: HisMipBaseRequest<MipSettleStatusQueryRequest>,
        env: String = NeusoftMipConfigs.DEFAULT_ENV
    ): HisMipBaseResult<MipSettleStatusQueryResponse> =
        runBlocking {
            getService(env).mipSettleStatusQuery(request.apply {
                this.funCode = "M003"
            })
        }


    /**
     * his 医保订单对账
     */
    fun mipAccounts(
        request: HisMipBaseRequest<MipAccountsRequest>,
        env: String = NeusoftMipConfigs.DEFAULT_ENV
    ): HisMipBaseResult<MipAccountsResponse> =
        runBlocking {
            getService(env).mipAccounts(request.apply {
                this.funCode = "M099"
            })
        }

    /**
     * his 医保退款
     */
    fun mipRefund(
        request: HisMipBaseRequest<MipRefundRequest>,
        env: String = NeusoftMipConfigs.DEFAULT_ENV
    ): HisMipBaseResult<MipRefundResult> =
        runBlocking {
            getService(env).mipRefund(request.apply {
                this.funCode = "M004"
            })
        }

}
