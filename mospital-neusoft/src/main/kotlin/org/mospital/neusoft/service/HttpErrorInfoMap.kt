package org.mospital.neusoft.service

import retrofit2.HttpException
import java.io.EOFException
import java.net.ConnectException
import java.net.SocketException
import java.net.SocketTimeoutException
import java.net.UnknownHostException
import javax.net.ssl.SSLException

class HttpErrorInfoMap {

    private val map = mapOf(
        HttpException::class to DefaultError(400, "网络错误，请检查您的网络设置"),
        SocketTimeoutException::class to DefaultError(408, "His返回超时，请稍后再试"),
        UnknownHostException::class to DefaultError(400, "无法连接到His医保服务"),
        ConnectException::class to DefaultError(500, "连接His医保服务失败，请稍后再试"),
        EOFException::class to DefaultError(500, "连接His医保服务失败，请稍后再试"),
        SocketException::class to DefaultError(500, "连接His医保服务失败，请稍后再试"),
        IllegalArgumentException::class to DefaultError(400, "参数错误"),
        SSLException::class to DefaultError(500, "His医保服务安全证书错误")
    )

    fun getErrorInfo(e: Exception): DefaultError {
        return map[e::class] ?: DefaultError(500, "未知错误")
    }

    class DefaultError(var code: Int, var message: String)
}
