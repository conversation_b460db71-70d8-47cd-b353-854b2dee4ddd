package org.mospital.neusoft.service

import org.mospital.neusoft.mip.HisMipBaseRequest
import org.mospital.neusoft.mip.HisMipBaseResult
import org.mospital.neusoft.mip.MipAccountsRequest
import org.mospital.neusoft.mip.MipAccountsResponse
import org.mospital.neusoft.mip.MipRefundRequest
import org.mospital.neusoft.mip.MipRefundResult
import org.mospital.neusoft.mip.MipSettleCallBackRequest
import org.mospital.neusoft.mip.MipSettleCallBackResult
import org.mospital.neusoft.mip.MipSettleRequest
import org.mospital.neusoft.mip.MipSettleResponse
import org.mospital.neusoft.mip.MipSettleStatusQueryRequest
import org.mospital.neusoft.mip.MipSettleStatusQueryResponse


import retrofit2.http.Body
import retrofit2.http.POST

/**
 * 基础信息业务交互接口，接口路径中的 PARTNER_ID 在拦截器中替换
 */
interface HisMipStanderService {

    /**
     * 上传并下单处方
     */
    @POST("/MPService.asmx")
    suspend fun mipSettle(@Body request: HisMipBaseRequest<MipSettleRequest>): HisMipBaseResult<MipSettleResponse>


    /**
     * 结算结果回调
     */
    @POST("/MPService.asmx")
    suspend fun settleCallBack(@Body request: HisMipBaseRequest<MipSettleCallBackRequest>): HisMipBaseResult<MipSettleCallBackResult>


    /**
     * 查询医保结算状态
     */
    @POST("/MPService.asmx")
    suspend fun mipSettleStatusQuery(@Body request: HisMipBaseRequest<MipSettleStatusQueryRequest>): HisMipBaseResult<MipSettleStatusQueryResponse>


    /**
     * 对账
     */
    @POST("/MPService.asmx")
    suspend fun mipAccounts(@Body request: HisMipBaseRequest<MipAccountsRequest>): HisMipBaseResult<MipAccountsResponse>


    /**
     * 退款
     */
    @POST("/MPService.asmx")
    suspend fun mipRefund(@Body request: HisMipBaseRequest<MipRefundRequest>): HisMipBaseResult<MipRefundResult>

}
