package org.mospital.neusoft.service


import okhttp3.Interceptor
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import okio.Buffer

class HisMipSoapRequestConvertInterceptor : Interceptor {

    companion object {
        private val MEDIA_TYPE_XML = ("text/xml; charset=utf-8".toMediaType())

        private const val SOAP_ENVELOPE_START = """<?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"><soap:Body>"""
        private const val SOAP_ENVELOPE_END = "</soap:Body></soap:Envelope>"
        private const val CALL_SERVICE_START = """<CallService xmlns="http://tempuri.org/"><jsonInData>"""
        private const val CALL_SERVICE_END = "</jsonInData></CallService>"
    }

    override fun intercept(chain: Interceptor.Chain): Response {

        var originalRequest = chain.request()
        val originalRequestBody: RequestBody = originalRequest.body!!
        val requestJson = extractRequestJson(originalRequestBody)
        val soapXml = jsonToSoapXml(requestJson)
        val newRequest = buildSoapRequest(originalRequest, soapXml)
        return chain.proceed(newRequest)

    }

    private fun extractRequestJson(body: RequestBody): String {
        val buffer = Buffer()
        body.writeTo(buffer)
        return buffer.readUtf8()
    }

    private fun buildSoapRequest(request: Request, soapXml: String): Request {
        val newRequestBody: RequestBody = soapXml.toRequestBody(MEDIA_TYPE_XML)
        return request.newBuilder()
            .header("SOAPAction", "http://tempuri.org/CallService")
            .post(newRequestBody)
            .build()
    }

    private fun jsonToSoapXml(jsonString: String): String {
        return buildString {
            append(SOAP_ENVELOPE_START)
            append(CALL_SERVICE_START)
            append(jsonString)
            append(CALL_SERVICE_END)
            append(SOAP_ENVELOPE_END)
        }
    }


}
