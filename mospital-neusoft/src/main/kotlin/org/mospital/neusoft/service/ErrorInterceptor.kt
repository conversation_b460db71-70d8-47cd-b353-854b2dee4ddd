package org.mospital.neusoft.service

import com.fasterxml.jackson.databind.ObjectMapper
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.ResponseBody.Companion.toResponseBody
import org.mospital.jackson.JacksonKit


class ErrorInterceptor : Interceptor {

    companion object {
        private val MEDIA_TYPE_JSON = "application/json; charset=UTF-8".toMediaType()
    }

    override fun intercept(chain: Interceptor.Chain): Response {

        val request = chain.request()

        try {
            val response = chain.proceed(request)
            return response.newBuilder()
                .body(response.body)
                .build()
        } catch (e: Exception) {
            val error = HttpErrorInfoMap().getErrorInfo(e)
            val errorJson = JacksonKit.writeValueAsString(error)
            return Response.Builder()
                .request(request)
                .protocol(Protocol.HTTP_1_1)
                .code(200)
                .message("ok")
                //自己构建body
                .body(errorJson.toResponseBody(MEDIA_TYPE_JSON))
                .build()
        }
    }

}
