package org.mospital.mip.mobile.bean.request

/**
 * 5.2.2 支付下单 请求类
 */
class PayOrderRequest {

    /**
     * 支付授权码。电子凭证线上渠道授权返回,与电子凭证三要素不可同时为空
     * 参数类型：字符型(40)
     * 是否必填：N
     */
    var payAuthNo: String = ""

    /**
     * 待支付订单号。费用上传返回
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var payOrdId: String = ""

    /**
     * 支付订单对应的 token。费用上传返回
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var payToken: String = ""

    /**
     * 定点机构编码。
     * 参数类型：字符型(12)
     * 是否必填：Y
     */
    var orgCodg: String = ""

    /**
     * 业务流水号。每一次请求唯一
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var orgBizSer: String = ""

    /**
     * 收费批次号。与费用上传一致
     * 参数类型：字符型(40)
     * 是否必填：N
     */
    var chrgBchno: String = ""

    /**
     * 费用类别。与费用上传一致
     * 参数类型：字符型(2)
     * 是否必填：Y
     */
    var feeType: String = ""

    /**
     * 就诊事件 ID。与费用上传一致
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var mdtrtId: String = ""

    /**
     * 住院押金。住院结算时院内已经缴纳住院押金金额，住院结算可抵扣现金支付部份，大于现金支付部份需要院内自行进行退费，移动支付中心不进行处理
     * 参数类型：数据型(16,2)
     * 是否必填：N
     */
    var deposit: String = ""

    /**
     * 扩展数据。可参考 FSI 的接口要求
     * 参数类型：字符型(4000)
     * 是否必填：N
     */
    var expContent: String = ""

}