package org.mospital.mip.mobile

import okhttp3.Interceptor
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.Response
import okhttp3.ResponseBody.Companion.toResponseBody
import org.mospital.common.IdUtil
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import retrofit2.Invocation

class MobilePaySignInterceptor(private val dataHandler: DataHandler): Interceptor {

    companion object {
        private val mediaType = "application/json;chartset=uft-8".toMediaType()
        private val log: Logger = LoggerFactory.getLogger(MobilePaySignInterceptor::class.java)
    }

    fun RequestBody?.bodyToString(): String {
        if (this == null) return ""
        val buffer = okio.Buffer()
        writeTo(buffer)
        return buffer.readUtf8()
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        val traceId = IdUtil.simpleUUID()

        val request: Request = chain.request()
        val invocation: Invocation = request.tag(Invocation::class.java) as Invocation
        val requestBody: Any = invocation.arguments()[0] as Any

        // 获取请求数据体，构建包装(加密、签名)后再发起请求
        val dataStr = request.body.bodyToString()
        log.debug("TraceId#${traceId} 【移动支付中心】请求参数明文 $dataStr")
        val encryptData: String = dataHandler.buildReqData(requestBody)
        log.debug("TraceId#${traceId} 【移动支付中心】请求参数参数 $encryptData")

        // 发起请求
        val newRequest = request
            .newBuilder()
            .post(encryptData.toRequestBody(mediaType))
            .build()
        val response = chain.proceed(newRequest)
        val responseBodyStr = response.body?.string().orEmpty()
        log.debug("TraceId#${traceId} 【移动支付中心】请求参数结果 $responseBodyStr")

        // 对响应进行解密
        val decryptData = dataHandler.processRspData(responseBodyStr)
        log.debug("TraceId#${traceId} 移动支付中心接口响应解密结果 -> $decryptData")

        return response.newBuilder().body(decryptData.toResponseBody(mediaType)).build()
    }

}