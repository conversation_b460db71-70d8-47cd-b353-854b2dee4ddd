package org.mospital.mip.mobile.bean.result

/**
 * 5.2.2 支付下单 响应类
 */
class PayOrderResult {

    /**
     * 。
     * 参数类型：支付订单号字符型(40)
     * 是否必填：Y
     */
    var payOrdId: String = ""

    /**
     * 订单状态。字典订单状态(ordStas)
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var ordStas: String = ""

    /**
     * 费用总额。
     * 参数类型：数值型(12,2)
     * 是否必填：Y
     */
    var feeSumamt: String = ""

    /**
     * 现金支付。
     * 参数类型：数值型(12,2)
     * 是否必填：Y
     */
    var ownPayAmt: String = ""

    /**
     * 个人账户支出。
     * 参数类型：数值型(12,2)
     * 是否必填：Y
     */
    var psnAcctPay: String = ""

    /**
     * 医保基金支付。
     * 参数类型：医保基金支付数值型(12,2)
     * 是否必填：Y
     */
    var fundPay: String = ""

    /**
     * 住院押金。根据下单传入计算，最大值不超过自费现金需要支付金额
     * 参数类型：数据型(16,2)
     * 是否必填：N
     */
    var deposit: String = ""

    /**
     * 扩展数据
     * 参数类型：extData(扩展数据)
     * 是否必填：N
     */
    var extData: ExtDataBean = ExtDataBean()

    class ExtDataBean {
        var preSetl: PreSetlBean = PreSetlBean()
    }

    class PreSetlBean {
        /**
         *
         * 参数示例：0.00
         */
        var cvlserv_pay: String = ""

        /**
         *
         * 参数示例：0.00
         */
        var hifdm_pay: String = ""

        /**
         *
         * 参数示例：0
         */
        var cvlserv_flag: String = ""

        /**
         *
         * 参数示例：11
         */
        var med_type: String = ""

        /**
         *
         * 参数示例：01
         */
        var naty: String = ""

        /**
         *
         * 参数示例：19.00
         */
        var psn_cash_pay: String = ""

        /**
         *
         * 参数示例：654225199806182514
         */
        var certno: String = ""

        /**
         *
         * 参数示例：0.00
         */
        var hifmi_pay: String = ""

        /**
         *
         * 参数示例：65000001000000000110898070
         */
        var psn_no: String = ""

        /**
         *
         * 参数示例：19.00
         */
        var act_pay_dedc: String = ""

        /**
         *
         * 参数示例：02
         */
        var mdtrt_cert_type: String = ""

        /**
         *
         * 参数示例：0.00
         */
        var balc: String = ""

        /**
         *
         * 参数示例：01
         */
        var psn_cert_type: String = ""

        /**
         *
         * 参数示例：0
         */
        var acct_mulaid_pay: String = ""

        /**
         *
         * 参数示例：1
         */
        var clr_way: String = ""

        /**
         *
         * 参数示例：0.00
         */
        var hifob_pay: String = ""

        /**
         *
         * 参数示例：0.00
         */
        var oth_pay: String = ""

        /**
         *
         * 参数示例：19.00
         */
        var medfee_sumamt: String = ""

        /**
         *
         * 参数示例：0.00
         */
        var hifes_pay: String = ""

        /**
         *
         * 参数示例：1
         */
        var gend: String = ""

        /**
         *
         * 参数示例：65012022092100262101
         */
        var mdtrt_id: String = ""

        /**
         *
         * 参数示例：0.00
         */
        var acct_pay: String = ""

        /**
         *
         * 参数示例：0.00
         */
        var fund_pay_sumamt: String = ""

        /**
         *
         * 参数示例：0.00
         */
        var fulamt_ownpay_amt: String = ""

        /**
         *
         * 参数示例：0.00
         */
        var hosp_part_amt: String = ""

        /**
         *
         * 参数示例：19.00
         */
        var inscp_scp_amt: String = ""

        /**
         *
         * 参数示例：310
         */
        var insutype: String = ""

        /**
         *
         * 参数示例：0.00
         */
        var maf_pay: String = ""

        /**
         *
         * 参数示例：19.00
         */
        var psn_part_amt: String = ""

        /**
         *
         * 参数示例：650100
         */
        var clr_optins: String = ""

        /**
         *
         * 参数示例：0.00
         */
        var pool_prop_selfpay: String = ""

        /**
         *
         * 参数示例：1101
         */
        var psn_type: String = ""

        /**
         *
         * 参数示例：0.00
         */
        var hifp_pay: String = ""

        /**
         *
         * 参数示例：0.00
         */
        var overlmt_selfpay: String = ""

        /**
         *
         * 参数示例：0.00
         */
        var preselfpay_amt: String = ""

        /**
         *
         * 参数示例：9901
         */
        var clr_type: String = ""

    }

}