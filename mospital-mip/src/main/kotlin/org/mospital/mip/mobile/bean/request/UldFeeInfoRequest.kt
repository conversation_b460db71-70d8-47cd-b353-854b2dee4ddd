package org.mospital.mip.mobile.bean.request

/**
 * 5.2.1 费用明细上传 请求类
 */
class UldFeeInfoRequest {

    /**
     * 机构编码。医保分配
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var orgCodg: String = ""

    /**
     * 电子凭证机构号。电子凭证中台分配，待机构电子凭证建设完毕后可获取该机构号
     * 参数类型：字符型(40)
     * 是否必填：N
     */
    var orgId: String? = ""

    /**
     * 人员编号。
     * 参数类型：字符型(30)
     * 是否必填：Y
     */
    var psnNo: String = ""

    /**
     * 险种类型。字典险种类型(insutype)
     * 参数类型：字符型(6)
     * 是否必填：Y
     */
    var insutype: String = ""

    /**
     * 医疗机构订单号。院内产生惟一流水，可关联到一次结算记录，结算成功回调入参返回
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var medOrgOrd: String = ""

    /**
     * 要续方的原处方流水。rxCircFlag 为 1 时必传，续方时必传
     * 参数类型：字符型(40)
     * 是否必填：N
     */
    var initRxOrd: String? = ""

    /**
     * 电子处方流转标志。
     * 参数类型：字符型(1)
     * 是否必填：N
     */
    var rxCircFlag: String? = ""

    /**
     * 开始时间。挂号时间yyyy-MM-dd HH:mm:ss
     * 参数类型：字符型(19)
     * 是否必填：Y
     */
    var begntime: String = ""

    /**
     * 证件号码。
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var idNo: String = ""

    /**
     * 用户姓名。
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var userName: String = ""

    /**
     * 证件类别。字典人员证件类型(psn_cert_type)
     * 参数类型：字符型(3)
     * 是否必填：Y
     */
    var idType: String = ""

    /**
     * 电子凭证解码返回
     * 参数类型：电子凭证授权ecToken字符型(64)
     * 是否必填：Y
     */
    var ecToken: String = ""

    /**
     * 参保地。电子凭证解码返回
     * 参数类型：字符型(6)
     * 是否必填：N
     */
    var insuCode: String? = ""

    /**
     * 住院/门诊号。院内唯一流水
     * 参数类型：字符型(30)
     * 是否必填：Y
     */
    var iptOtpNo: String = ""

    /**
     * 医师编码。
     * 参数类型：字符型(30)
     * 是否必填：N
     */
    var atddrNo: String? = ""

    /**
     * 医师姓名。
     * 参数类型：字符型(50)
     * 是否必填：N
     */
    var drName: String? = ""

    /**
     * 科室编码。
     * 参数类型：字符型(30)
     * 是否必填：N
     */
    var deptCode: String? = ""

    /**
     * 科室名称。
     * 参数类型：字符型(100)
     * 是否必填：Y
     */
    var deptName: String = ""

    /**
     * 科别。
     * 参数类型：字符型(6)
     * 是否必填：Y
     */
    var caty: String = ""

    /**
     * 就诊 ID。payAutoNo 为空时必传，调用医保核心做就诊登记获取。
     * 参数类型：字符型(30)
     * 是否必填：Y
     */
    var mdtrtId: String? = ""

    /**
     * 医疗类别。字典医疗类别(med_type)
     * 参数类型：字符型(6)
     * 是否必填：Y
     */
    var medType: String = ""

    /**
     * 费用类别。字典费用类型（feeType）
     * 参数类型：字符型(3)
     * 是否必填：Y
     */
    var feeType: String = ""

    /**
     * 医疗费总额。
     * 参数类型：数值型(16,2)
     * 是否必填：Y
     */
    var medfeeSumamt: String = ""

    /**
     * 个人账户使用标志。药店上传费用时必填
     * 参数类型：字符型(1)
     * 是否必填：Y
     */
    var acctUsedFlag: String = ""

    /**
     * 主要病情描述。
     * 参数类型：字符型(1000)
     * 是否必填：N
     */
    var mainCondDscr: String? = ""

    /**
     * 病种编码。按照标准编码填写：按病种结算病种目录代码(bydise_setl_list_code)、门诊慢特病病种目录代码(opsp_dise_cod)、
     * 参数类型：字符型(30)
     * 是否必填：Y
     */
    var diseCodg: String = ""

    /**
     * 病种名称。
     * 参数类型：字符型(500)
     * 是否必填：N
     */
    var diseName: String? = ""

    /**
     * 个人结算方式。
     * 参数类型：字符型(6)
     * 是否必填：Y
     */
    var psnSetlway: String = ""

    /**
     * 收费批次号。
     * 参数类型：字符型(30)
     * 是否必填：Y
     */
    var chrgBchno: String = ""

    /**
     * 公立医院改革标志。可参考 FSI 的接口要求
     * 参数类型：字符型(6)
     * 是否必填：Y
     */
    var pubHospRfomFlag: String = ""

    /**
     * 发票号。
     * 参数类型：字符型(20)
     * 是否必填：N
     */
    var invono: String? = ""

    /**
     * 出院时间。yyyy-MM-dd HH:mm:ss
     * 参数类型：字符型(19)
     * 是否必填：N
     */
    var endtime: String? = ""

    /**
     * 全自费金额。住院结算时需要
     * 参数类型：数值型(16,2)
     * 是否必填：N
     */
    var fulamtOwnpayAmt: String? = ""

    /**
     * 超限价金额。住院结算时需要
     * 参数类型：数值型(16,2)
     * 是否必填：N
     */
    var overlmtSelfpay: String? = ""

    /**
     * 先行自付金额。住院结算时需要
     * 参数类型：数值型(16,2)
     * 是否必填：N
     */
    var preselfpayAmt: String? = ""

    /**
     * 符合政策范围金额。住院结算时需要
     * 参数类型：数值型(16,2)
     * 是否必填：N
     */
    var inscpScpAmt: String? = ""

    /**
     * 手术操作代码。住院结算时需要
     * 参数类型：字符型(30)
     * 是否必填：N
     */
    var oprnOprtCode: String? = ""

    /**
     * 手术操作名称。住院结算时需要
     * 参数类型：字符型(500)
     * 是否必填：N
     */
    var oprnOprtName: String? = ""

    /**
     * 计划生育服务证号。住院结算时需要
     * 参数类型：字符型(50)
     * 是否必填：N
     */
    var fpscNo: String? = ""

    /**
     * 晚育标志。住院结算时需要
     * 参数类型：字符型(3)
     * 是否必填：N
     */
    var latechbFlag: String? = ""

    /**
     * 孕周数。住院结算时需要
     * 参数类型：数值型(2)
     * 是否必填：N
     */
    var gesoVal: String? = ""

    /**
     * 胎次。住院结算时需要
     * 参数类型：数值型(3)
     * 是否必填：Y
     */
    var fetts: String? = ""

    /**
     * 胎儿数。住院结算时需要
     * 参数类型：数值型(3)
     * 是否必填：N
     */
    var fetusCnt: String? = ""

    /**
     * 早产标志。住院结算时需要
     * 参数类型：字符型(3)
     * 是否必填：N
     */
    var pretFlag: String? = ""

    /**
     * 计划生育手术类别。生育门诊按需录入
     * 参数类型：字符型(6)
     * 是否必填：N
     */
    var birctrlType: String? = ""

    /**
     * 生育门诊按需录入，yyyy-MM-dd
     * 参数类型：birctrlMatnDate计划生育手术或生育日期字符型(10)
     * 是否必填：N
     */
    var birctrlMatnDate: String? = ""

    /**
     * 伴有并发症标志。住院结算时需要
     * 参数类型：字符型(3)
     * 是否必填：N
     */
    var copFlag: String? = ""

    /**
     * 出院科室编码。住院结算时需要
     * 参数类型：字符型(30)
     * 是否必填：N
     */
    var dscgDeptCodg: String? = ""

    /**
     * 出院科室名称。住院结算时需要
     * 参数类型：字符型(100)
     * 是否必填：N
     */
    var dscgDeptName: String? = ""

    /**
     * 出院床位。住院结算时需要
     * 参数类型：字符型(50)
     * 是否必填：N
     */
    var dscgDed: String? = ""

    /**
     * 离院方式。住院结算时需要
     * 参数类型：字符型(8)
     * 是否必填：N
     */
    var dscgWay: String? = ""

    /**
     * 死亡日期。yyyy-MM-dd
     * 参数类型：字符型(10)
     * 是否必填：N
     */
    var dieDate: String? = ""

    /**
     * 生育类别。住院结算时需要
     * 参数类型：字符型(6)
     * 是否必填：Y
     */
    var matnType: String? = ""

    /**
     * 扩展参数。可参考 FSI 的接口要求
     * 参数类型：字符型(4000)
     * 是否必填：N
     */
    var expContent: String? = ""

    /**
     * 中途结算标志。字典中途结算标志（mid_setl_flag）
     * 参数类型：字符型(3)
     * 是否必填：N
     */
    var midSetlFlag: String? = ""

    /**
     * 诊断或症状明细
     */
    var diseinfoList: List<DiseinfoBean> = listOf()

    /**
     * 费用明细
     */
    var feedetailList: List<FeeDetailBean> = listOf()

    /**
     * 入院诊断描述。住院结算时需要
     * 参数类型：字符型(200)
     * 是否必填：N
     */
    var admDiagDscr: String? = ""

    /**
     * 入院科室编码。住院结算时需要
     * 参数类型：字符型(30)
     * 是否必填：N
     */
    var admDeptCodg: String? = ""

    /**
     * 入院科室名称。住院结算时需要
     * 参数类型：字符型(100)
     * 是否必填：N
     */
    var admDeptName: String? = ""

    /**
     * 入院床位。住院结算时需要
     * 参数类型：字符型(30)
     * 是否必填：N
     */
    var admBed: String? = ""

    /**
     * 支付授权码。线上授权返回，与 ecToken 不可同时为空
     * 参数类型：字符型(40)
     * 是否必填：N
     */
    var payAuthNo: String? = ""

    /**
     * 经纬度。格式:经度,纬度如：
     * 参数类型：字符型(21)
     * 是否必填：N
     */
    var uldLatlnt: String? = ""

    /**
     * 就诊凭证类型。“00” 无就诊介质，“01” 电子凭证，“02”居民身份证，“03”社会保障卡
     * 参数类型：字符型(2)
     * 是否必填：N
     */
    var mdtrtCertType: String? = ""

    /**
     * 用户参保地行政区划。无值(null)时取用户电子凭证设置的默认参保地
     * 参数类型：字符型(6)
     * 是否必填：N
     */
    var insuplcAdmdvs: String? = null

    class DiseinfoBean {
        /**
         * 诊断类别。字典诊断类别(diag_type)
         * 参数类型：字符型(3)
         * 是否必填：Y
         */
        var diagType: String = ""

        /**
         * 诊断排序号。见字典定义
         * 参数类型：数值型(2)
         * 是否必填：Y
         */
        var diagSrtNo: String = ""

        /**
         * 诊断代码。-
         * 参数类型：字符型(20)
         * 是否必填：Y
         */
        var diagCode: String = ""

        /**
         * 诊断名称。-
         * 参数类型：字符型(100)
         * 是否必填：Y
         */
        var diagName: String = ""

        /**
         * 诊断科室。
         * 参数类型：字符型(50)
         * 是否必填：Y
         */
        var diagDept: String = ""

        /**
         * 诊断医生编码。
         * 参数类型：字符型(30)
         * 是否必填：Y
         */
        var diseDorNo: String = ""

        /**
         * 诊断医生姓名。
         * 参数类型：字符型(50)
         * 是否必填：Y
         */
        var diseDorName: String = ""

        /**
         * 诊断时间。yyyy-MM-ddHH:mm:ss
         * 参数类型：字符型(19)
         * 是否必填：Y
         */
        var diagTime: String = ""

        /**
         * 有效标志。
         * 参数类型：字符型(3)
         * 是否必填：Y
         */
        var valiFlag: String = ""

    }

    class FeeDetailBean {
        /**
         * 费用明细流水号。单次就诊内唯一
         * 参数类型：字符型(30)
         * 是否必填：Y
         */
        var feedetlSn: String = ""

        /**
         * 就诊 ID。
         * 参数类型：字符型(30)
         * 是否必填：N
         */
        var mdtrtId: String? = ""

        /**
         * 人员编号。
         * 参数类型：字符型(30)
         * 是否必填：Y
         */
        var psnNo: String = ""

        /**
         * 收费批次号。同一收费批次号病种编号必须一致
         * 参数类型：字符型(30)
         * 是否必填：Y
         */
        var chrgBchno: String = ""

        /**
         * 病种编码。按照标准编码填写。
         * 按病种结算病种目录代码(bydise_setl_list_code)、门诊慢特病病种目录代码(opsp_dise_cod)
         * 参数类型：字符型(30)
         * 是否必填：N
         */
        var diseCodg: String? = ""

        /**
         * 病种编码。按照标准编码填写。
         * 按病种结算病种目录代码(bydise_setl_list_code)、门诊慢特病病种目录代码(opsp_dise_cod)
         * 参数类型：字符型(30)
         * 是否必填：N
         */
        var diseName: String? = ""

        /**
         * 处方号。外购处方时，传入外购处方的处方号；非外购处方，传入医药机构处方
         * 参数类型：字符型(30)
         * 是否必填：N
         */
        var rxno: String? = ""

        /**
         * 外购处方标志。
         * 参数类型：字符型(3)
         * 是否必填：Y
         */
        var rxCircFlag: String = ""

        /**
         * 费用发生时间。yyyy-MM-ddHH:mm:ss
         * 参数类型：日期时间型
         * 是否必填：Y
         */
        var feeOcurTime: String = ""

        /**
         * 医疗目录编码。
         * 参数类型：字符型(50)
         * 是否必填：Y
         */
        var medListCodg: String = ""

        /**
         * 医药机构目录编码。
         * 参数类型：字符型(150)
         * 是否必填：Y
         */
        var medinsListCodg: String = ""

        /**
         * 明细项目费用总额。
         * 参数类型：数值型(16,2)
         * 是否必填：Y
         */
        var detItemFeeSumamt: String = ""

        /**
         * 数量。
         * 参数类型：数值型(16,4)
         * 是否必填：Y
         */
        var cnt: String = ""

        /**
         * 单价。
         * 参数类型：数值型(16,6)
         * 是否必填：Y
         */
        var pric: String = ""

        /**
         * 单次剂量描述。
         * 参数类型：字符型(200)
         * 是否必填：N
         */
        var sinDosDscr: String? = ""

        /**
         * 使用频次描述。
         * 参数类型：字符型(200)
         * 是否必填：N
         */
        var usedFrquDscr: String = ""

        /**
         * 周期天数。
         * 参数类型：数值型(4,2)
         * 是否必填：N
         */
        var prdDays: String? = ""

        /**
         * 用药途径描述。
         * 参数类型：字符型(200)
         * 是否必填：N
         */
        var medcWayDscr: String? = ""

        /**
         * 开单科室编码。
         * 参数类型：字符型(30)
         * 是否必填：Y
         */
        var bilgDeptCodg: String = ""

        /**
         * 开单科室名称。
         * 参数类型：字符型(100)
         * 是否必填：Y
         */
        var bilgDeptName: String = ""

        /**
         * 开单医生编码。按照标准编码填写
         * 参数类型：字符型(30)
         * 是否必填：Y
         */
        var bilgDrCodg: String = ""

        /**
         * 开单医师姓名。
         * 参数类型：字符型(50)
         * 是否必填：Y
         */
        var bilgDrName: String = ""

        /**
         * 受单科室编码。
         * 参数类型：字符型(30)
         * 是否必填：N
         */
        var acordDeptCodg: String? = ""

        /**
         * 受单科室名称。
         * 参数类型：字符型(100)
         * 是否必填：N
         */
        var acordDeptName: String? = ""

        /**
         * 受单医生编码。按照标准编码填写
         * 参数类型：字符型(30)
         * 是否必填：N
         */
        var ordersDrCode: String? = ""

        /**
         * 受单医生姓名。
         * 参数类型：字符型(50)
         * 是否必填：N
         */
        var ordersDrName: String? = ""

        /**
         * 医院审批标志。
         * 参数类型：字符型(3)
         * 是否必填：Y
         */
        var hospApprFlag: String = ""

        /**
         * 中药使用方式。
         * 参数类型：字符型(6)
         * 是否必填：N
         */
        var tcmdrugUsedWay: String? = ""

        /**
         * 外检标志。
         * 参数类型：字符型(3)
         * 是否必填：N
         */
        var etipFlag: String? = ""

        /**
         * 外检医院编码。按照标准编码填写
         * 参数类型：字符型(30)
         * 是否必填：N
         */
        var etipHospCode: String? = ""

        /**
         * 出院带药标志。
         * 参数类型：字符型(3)
         * 是否必填：N
         */
        var dscgTkdrugFlag: String? = ""

        /**
         * 生育费用标志。
         * 参数类型：字符型(6)
         * 是否必填：N
         */
        var matnFeeFlag: String? = ""

        /**
         * 原费用流水号。退单时传入被退单的费用明细流水号
         * 参数类型：字符型(30)
         * 是否必填：N
         */
        var initFeedetlSn: String? = ""

        /**
         * 医嘱号。
         * 参数类型：字符型(30)
         * 是否必填：N
         */
        var drordNo: String? = ""

        /**
         * 医疗类别。
         * 参数类型：字符型(6)
         * 是否必填：Y
         */
        var medType: String = ""

        /**
         * 备注。
         * 参数类型：字符型(500)
         * 是否必填：N
         */
        var memo: String? = ""

        /**
         * 扩展字段。可参考 FSI 的接口要求
         * 参数类型：字符型(5000)
         * 是否必填：N
         */
        var expContent: String? = ""

        /**
         * 医疗目录名称。
         * 参数类型：字符型(50)
         * 是否必填：Y
         */
        var medListName: String = ""

        /**
         * 医疗目录规格。药品项目才有，对于非药品情况转成空串
         * 参数类型：字符型(50)
         * 是否必填：Y
         */
        var medListSpc: String = ""

        /**
         * 组套编号.可参考 FSI 的接口要求。
         * 参数类型：字符型(3)
         */
        var combNo: String = ""

    }

}