package org.mospital.mip.mobile.bean.result

import com.fasterxml.jackson.databind.JsonNode

/**
 * 5.2.3 医保退费 响应类
 */
class RefundOrderResult {

    /**
     * 结算中心流水号。
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var refdSn: String = ""

    /**
     * 医保退费流水号。现金退费不返回
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var hiFefdSn: String = ""

    /**
     * 医保退费日期。现金退费不返回
     * 参数类型：字符型(8)
     * 是否必填：Y
     */
    var hiTrnsDate: String = ""

    /**
     * 医保退费时间。现金退费不返回
     * 参数类型：字符型(6)
     * 是否必填：Y
     */
    var hiTrnsTime: String = ""

    /**
     * 退费状态。SUCC:成功FAIL:失败EXP:异常，存在退医保及自费时有可能出现这个状态
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var refStatus: String = ""

    /**
     * 扩展数据
     * 参数类型：extData(扩展数据)
     * 是否必填：N
     */
    var extData: JsonNode? = null

    // >>>>> 字段用于填充返回给HIS，非移动支付中心接口返回

    /**
     * 现金退费状态
     */
    var cashRefundStatus: String = ""

    /**
     * 现金退款渠道单号
     */
    var cashRefundNo: String? = null

    // <<<<< 字段用于填充返回给HIS，非移动支付中心接口返回
}