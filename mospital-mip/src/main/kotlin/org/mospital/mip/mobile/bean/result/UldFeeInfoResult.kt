package org.mospital.mip.mobile.bean.result

/**
 * 5.2.1 费用明细上传 响应类
 */
class UldFeeInfoResult {

    /**
     * 支付订单号。医保结算中心订单号
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var payOrdId: String = ""

    /**
     * 支付 token。下单支付使用
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var payToken: String = ""

    /**
     * 医保支付收银台h5。见下方拼接说明
     * 参数类型：字符型(4000)
     * 是否必填：N
     */
    var cashierUrl: String = ""

    /**
     * 医保扩展数据
     */
    var extData: ExtDataBean = ExtDataBean()

    class ExtDataBean {

        /**
         * 就诊ID
         */
        var mdtrtId: String = ""

        var result: List<ResultBean> = listOf()

        var upload: List<UploadBean> = listOf()

    }

    class ResultBean {

        /**
         * 基本药物标志
         */
        var bas_medn_flag: String = ""

        /**
         * 医疗收费项目类别
         */
        var med_chrgitm_type: String = ""

        /**
         * 明细项目费用总额
         */
        var det_item_fee_sumamt: String = ""

        /**
         * 医保谈判药品标志
         */
        var hi_nego_drug_flag: String = ""

        /**
         * 全自费金额
         */
        var fulamt_ownpay_amt: String = ""

        /**
         * 数量
         */
        var cnt: String = ""

        /**
         * 单价
         */
        var pric: String = ""

        /**
         * 费用明细流水号
         */
        var feedetl_sn: String = ""

        /**
         * 符合政策范围金额
         */
        var inscp_scp_amt: String = ""

        /**
         * 直报标志
         */
        var drt_reim_flag: String = ""

        /**
         * 超限价金额
         */
        var overlmt_amt: String = ""

        /**
         * 定价上限金额
         */
        var pric_uplmt_amt: String = ""

        /**
         * 自付比例
         */
        var selfpay_prop: String = ""

        /**
         * 先行自付金额
         */
        var preselfpay_amt: String = ""

        /**
         * 限制使用标志
         */
        var lmt_used_flag: String = ""

        /**
         * 收费项目等级
         */
        var chrgitm_lv: String = ""
    }

    class UploadBean {

        /**
         * 费用明细流水号
         */
        var feedetlSn: String = ""

        /**
         * 基本药物标志
         */
        var basMednFlag: String = ""

        /**
         * 先行自付金额
         */
        var preselfpayAmt: String = ""

        /**
         * 自付比例
         */
        var selfpayProp: String = ""

        /**
         * 医疗收费项目类别
         */
        var medChrgitmType: String = ""

        /**
         * 数量
         */
        var cnt: String = ""

        /**
         * 单价
         */
        var pric: String = ""

        /**
         * 超限价金额
         */
        var overlmtAmt: String = ""

        /**
         * 定价上限金额
         */
        var pricUplmtAmt: String = ""

        /**
         * 收费项目等级
         */
        var chrgitmLv: String = ""

        /**
         * 符合政策范围金额
         */
        var inscpScpAmt: String = ""

        /**
         * 明细项目费用总额
         */
        var detItemFeeSumamt: String = ""

        /**
         * 限制使用标志
         */
        var lmtUsedFlag: String = ""

        /**
         * 全自费金额
         */
        var fulamtOwnpayAmt: String = ""

        /**
         * 医保谈判药品标志
         */
        var hiNegoDrugFlag: String = ""

        /**
         * 直报标志
         */
        var drtReimFlag: String = ""

    }

}