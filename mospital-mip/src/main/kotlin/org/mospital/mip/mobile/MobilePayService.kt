package org.mospital.mip.mobile

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.MapperFeature
import kotlinx.coroutines.runBlocking
import org.dromara.hutool.core.bean.copier.BeanCopier
import org.dromara.hutool.core.bean.copier.CopyOptions
import org.mospital.common.http.HttpClientFactory
import org.mospital.jackson.JacksonKit
import org.mospital.mip.mobile.bean.request.*
import org.mospital.mip.mobile.bean.result.*
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import retrofit2.Retrofit
import retrofit2.converter.jackson.JacksonConverterFactory
import retrofit2.http.Body
import retrofit2.http.POST


interface MobilePayService {

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(MobilePayService::class.java)
        private val serviceCache = mutableMapOf<String, MobilePayService>()

        private fun createService(dataHandler: DataHandler): MobilePayService {
            val httpClient = HttpClientFactory.createWithRequestIdLogging(
                logger = logger,
                connectTimeout = 10_000,
                readTimeout = 20_000,
                useUuidRequestId = true
            ) {
                it.addInterceptor(MobilePaySignInterceptor(dataHandler))
            }

            val objectMapper = JacksonKit.buildMapper().apply {
                configure(MapperFeature.SORT_PROPERTIES_ALPHABETICALLY, true)
                setSerializationInclusion(JsonInclude.Include.NON_EMPTY)
            }

            val retrofit = Retrofit.Builder()
                .baseUrl(DataHandlerFactory.serviceUrl)
                .client(httpClient)
                .addConverterFactory(JacksonConverterFactory.create(objectMapper))
                .build()
            return retrofit.create(MobilePayService::class.java)
        }

        @Synchronized
        fun getService(dataHandler: DataHandler): MobilePayService {
            val cacheKey = "${dataHandler.javaClass.simpleName}_${dataHandler.hashCode()}"
            return serviceCache.getOrPut(cacheKey) { createService(dataHandler) }
        }

        /**
         * 从配置文件中获取 wechat 配置，并构建 service
         */
        val wechat: MobilePayService by lazy { getService(DataHandlerFactory.wechat) }

        /**
         * 从配置文件中获取 alipay 配置，并构建 service
         */
        val alipay: MobilePayService by lazy { getService(DataHandlerFactory.alipay) }

        /**
         * 构造费用明细上传
         * @param data 上传费用明细数据体。
         */
        fun buildUldFeeInfoRequest(
            data: Any,
            psnNo: String,
            insutype: String,
            insuplcAdmdvs: String,
            mdtrtId: String,
            payAuthNo: String,
            uldLatlnt: String
        ): UldFeeInfoRequest? {

            // 将结果 mapping 到 UldFeeInfoRequest
            val uldFeeInfoRequest = BeanCopier.of(data, UldFeeInfoRequest(), CopyOptions()).copy()

            // 填充数据，如 appid、机构编码等信息
            val config = MobilePayConfigFactory.getConfig("wechat")
            uldFeeInfoRequest.apply {
                orgCodg = config.orgCodg
                // 从人员信息查询接口中获取
                this.psnNo = psnNo
                this.insutype = insutype
                this.payAuthNo = payAuthNo
                this.insuplcAdmdvs = insuplcAdmdvs
                feedetailList.forEach { it.psnNo = psnNo }
                // 就诊ID
                this.mdtrtId = mdtrtId

                // 个人结算方式。1 为按项目结算
                psnSetlway = "1"
                // 是否使用个人账户
                acctUsedFlag = "1"
                this.uldLatlnt = uldLatlnt
            }
            return uldFeeInfoRequest
        }

        fun queryWechatOrder(request: QueryOrderInfoRequest): BaseDataResult<QueryOrderInfoResult> = runBlocking {
            wechat.queryOrderInfo(request)
        }

        fun refundWechatOrder(request: RefundOrderRequest): BaseDataResult<RefundOrderResult> = runBlocking {
            wechat.refundOrder(request)
        }
    }

    /**
     * 5.2.1 费用明细上传
     * 定点医药机构向地方移动支付中心发起上传费用明细交易，由地方移动支付中心向地方
     * 医保核心系统发起验证后完成[2204]、[2301]、[6201]接口业务交互。定点医药机构需要修
     * 改处方时，需要先撤销上传，修改完成之后重新上传。
     */
    @POST("org/local/api/hos/uldFeeInfo")
    suspend fun uldFeeInfo(@Body request: UldFeeInfoRequest): BaseDataResult<UldFeeInfoResult>

    /**
     * 5.2.2 支付下单
     * 无收银台模式下，定点医药机构向地方移动支付中心发起医保移动支付下单请求，地方
     * 移动支付中心进行移动支付权限核验后，返回支付下单结果信息。
     */
    @POST("org/local/api/hos/pay_order")
    suspend fun payOrder(@Body request: PayOrderRequest): BaseDataResult<PayOrderResult>

    /**
     * 5.2.3 医保退费
     * 定点医药机构对已经完成移动支付交易的医保订单，发起退费交易，医保订单交易原路
     * 返还参保人医保账户或现金支付账户。
     */
    @POST("org/local/api/hos/refund_Order")
    suspend fun refundOrder(@Body request: RefundOrderRequest): BaseDataResult<RefundOrderResult>

    /**
     * 5.3.1 医保订单结算结果查询
     * 定点医药机构向地方移动支付中心发起医保订单结算结果查询，返回统筹基金支付、
     * 个人账户支付、现金支付等结算信息。
     */
    @POST("org/local/api/hos/query_order_info")
    suspend fun queryOrderInfo(@Body request: QueryOrderInfoRequest): BaseDataResult<QueryOrderInfoResult>

    /**
     * 5.4.1 费用明细上传撤销
     * 定点医药机构由于处方变更等情况，向地方移动支付中心发起费用明细上传撤销交易，
     * 由地方移动支付中心向地方医保核心系统发起验证后完成[2205]、[2302]接口业务交互。适
     * 用于已经上传但未结算确认的费用明细。
     */
    @POST("org/local/api/hos/revoke_order")
    suspend fun revokeOrder(@Body request: RevokeOrderRequest): BaseDataResult<RevokeOrderResult>
}