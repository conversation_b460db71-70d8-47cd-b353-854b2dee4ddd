package org.mospital.mip.mobile

/**
 * 移动支付配置项
 */
data class MobilePayConfig(
    /**
     * 当前配置分组名称
     */
    val configName: String,
    /**
     * 应用ID （渠道 ID）
     */
    val appId: String,
    /**
     * 数字密钥：对传输过程中的业务数据进行加解密的 SM4 密钥。
     */
    val sm4key: String,
    /**
     * 渠道私钥，【签名】
     */
    val channelPrivateKey: String,
    /**
     * 平台公钥：移动支付中台提供的非对称加密(SM2)的公钥，渠道方使用该公钥进行验签。
     */
    val platformPublicKey: String,
    /**
     * 定点医药机构名称
     */
    val orgName: String,
    /**
     * 定点医药机构编码
     */
    val orgCodg: String,
    /**
     * 机构渠道认证编码
     */
    val orgChnlCrtfCodg: String,
    /**
     * 移动支付中心接口地址
     */
    val mobilePayUrl: String
)