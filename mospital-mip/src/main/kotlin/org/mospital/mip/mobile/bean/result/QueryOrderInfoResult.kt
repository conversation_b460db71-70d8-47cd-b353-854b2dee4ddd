package org.mospital.mip.mobile.bean.result

import com.fasterxml.jackson.databind.JsonNode

/**
 * 5.3.1 医保订单结算结果查询 响应类
 */
class QueryOrderInfoResult {

    /**
     * 订单状态。字典订单状态(ordStas)
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var ordStas: String = ""

    /**
     * 支付订单号。医保结算中心订单号
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var payOrdId: String = ""

    /**
     * 回调类型。字典回调类型(callType)
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var callType: String = ""

    /**
     * 医院订单号。医院订单号
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var medOrgOrd: String = ""

    /**
     * 交易时间。交易成功时间
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var traceTime: String = ""

    /**
     * 两定机构编号
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var orgCodg: String = ""

    /**
     * 两定机构名称
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var orgName: String = ""

    /**
     * 结算类型。ALL:医保自费全部，CASH:只结现金 HI:只结医保
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var setlType: String = ""

    /**
     * 费用总金额数值型。
     * 参数类型：(12,2)
     * 是否必填：Y
     */
    var feeSumamt: String = ""

    /**
     * 现金支付 数值型。
     * 参数类型：(12,2)
     * 是否必填：Y
     */
    var ownPayAmt: String = ""

    /**
     * 个人账户支付数值型。
     * 参数类型：(12,2)
     * 是否必填：Y
     */
    var psnAcctPay: String = ""

    /**
     * 医保基金支付数值型。
     * 参数类型：(12,2)
     * 是否必填：Y
     */
    var fundPay: String = ""

    /**
     * 用于院内结算失败对医保的冲正授权字符型。1 小时内有效
     * 参数类型：(40)
     * 是否必填：Y
     */
    var revsToken: String = ""

    /**
     * 扩展数据
     * 参数类型：extData(扩展数据)
     * 是否必填：N
     */
    var extData: JsonNode? = null

    /**
     * 住院押金 数据型。根据下单传入计算，最大值不超过自费现金需要支付金额
     * 参数类型：(16,2)
     * 是否必填：N
     */
    var deposit: String = ""

    /**
     * 医保收费时间字符型。
     * 参数类型：(6)
     * 是否必填：Y
     */
    var hiChrgTime: String = ""

    /**
     * 医保交易流水号字符型。
     * 参数类型：(40)
     * 是否必填：Y
     */
    var hiDocSn: String = ""

    /**
     * 医保挂号流水号字符型。
     * 参数类型：(40)
     * 是否必填：Y
     */
    var hiRgstSn: String = ""

    /**
     * 电子凭证码值字符型。
     * 参数类型：(200)
     * 是否必填：N
     */
    var ecCode: String = ""

}