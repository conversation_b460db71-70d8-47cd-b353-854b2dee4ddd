package org.mospital.mip.mobile.bean

class MipNoticeResponse {

    /**
     * 成功标志，0=成功，1=失败
     */
    var code: Int = 0

    /**
     * 错误信息
     */
    var message: String = ""

    /**
     * 业务数据
     */
    var data: Map<String, Any> = mapOf()

    constructor(success: <PERSON><PERSON><PERSON>, message: String) {
        this.code = if (success) 0 else 1
        this.message = message
        this.data = mapOf(
            "success" to success,
            "message" to message
        )
    }

}