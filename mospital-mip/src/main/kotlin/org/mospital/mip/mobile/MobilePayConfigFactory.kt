package org.mospital.mip.mobile

import org.mospital.mip.FsiRootConfigLoader
import java.util.concurrent.ConcurrentHashMap

/**
 * 移动支付中心 配置工厂类
 */
object MobilePayConfigFactory {

    private val configMap = ConcurrentHashMap<String, MobilePayConfig>()
    private val threadLocalHolder = ThreadLocal<MobilePayConfig>()

    private fun fetchConfig(configName: String): MobilePayConfig {
        val fsiConfig = FsiRootConfigLoader.config
        val specificEnvConfig = when (configName.lowercase()) {
            "wechat" -> fsiConfig.wechat
            // "alipay" -> fsiConfig.alipay // Assuming alipay might be a future group
            else -> throw IllegalArgumentException("MobilePayConfigFactory: Configuration for '$configName' not found in fsi.yaml")
        }

        // Type cast needed if specificEnvConfig is of a common supertype or Any
        val wechatConf = specificEnvConfig


        return MobilePayConfig(
            configName = configName,
            appId = wechatConf.appId,
            sm4key = wechatConf.sm4Key, // Ensure MobilePayConfig uses sm4key (lowercase k) or adjust here
            channelPrivateKey = wechatConf.channelPrivateKey,
            platformPublicKey = wechatConf.platformPublicKey,
            orgName = wechatConf.orgName,
            orgCodg = wechatConf.orgCodg,
            orgChnlCrtfCodg = wechatConf.orgChnlCrtfCodg,
            mobilePayUrl = fsiConfig.mobilePayUrl
        )
    }

    fun getConfig(configName: String): MobilePayConfig {
        return configMap.computeIfAbsent(configName) { fetchConfig(configName) }
    }

    var threadLocalConfig: MobilePayConfig
        get() = threadLocalHolder.get()
        set(value) {
            threadLocalHolder.set(value)
        }

    val wechat: MobilePayConfig by lazy { getConfig("wechat") }
    val alipay: MobilePayConfig by lazy { getConfig("alipay") } // Similar to DataHandlerFactory, this might fail for alipay
}