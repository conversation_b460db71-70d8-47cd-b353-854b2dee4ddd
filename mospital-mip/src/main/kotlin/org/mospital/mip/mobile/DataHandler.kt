package org.mospital.mip.mobile

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.tencent.mip.exception.FuncRetCode
import com.tencent.mip.exception.ServerException
import com.tencent.mip.util.CommonUtil
import org.dromara.hutool.core.codec.binary.Base64
import org.dromara.hutool.core.codec.binary.Hex
import org.slf4j.LoggerFactory
import java.util.*

/**
 * 重写腾讯 DataHandler，避免单例影响多个应用使用
 */
class DataHandler(
    private val appId: String,
    private val secret: String,
    publicKey: String,
    privateKey: String
) {

    companion object {
        private val LOG = LoggerFactory.getLogger(DataHandler::class.java)
    }

    private val encryptKeys: ByteArray = run {
        val appIdHex = CommonUtil.stringToHexString(appId.take(16))
        SM4Util.encryptEcb(appIdHex, secret).take(16).uppercase().toByteArray()
    }

    private val publicKeys: String
    private val privateKeys: String
    private val sm2 = SM2Util()
    var version = "2.0.1"

    init {
        publicKeys = Hex.encodeStr(Base64.decode(publicKey))
        privateKeys = Hex.encodeStr(Base64.decode(privateKey))
    }

    fun buildReqData(objData: JSONObject): String {
        val map: MutableMap<String, Any?> = TreeMap()
        val iterator = objData.entries.iterator()
        while (iterator.hasNext()) {
            val (key, value) = iterator.next()
            map[key] = value
        }
        return buildReqData(map as Map<*, *>)
    }

    fun buildReqData(objData: Any): String {
        val map: Map<String, Any> = CommonUtil.objectToMap(objData)
        LOG.debug("buildReqData参数转换map结果：$map")
        return buildReqData(map)
    }

    fun buildReqData(mapData: Map<String, Any?>): String {
        val str = JSON.toJSONString(mapData)
        val jsonObject = org.json.JSONObject(str)
        val jsonStr = CommonUtil.valueToString(jsonObject)
        val dataMap: Map<String, Any> = JSON.parseObject(jsonStr)
        val treeMap: MutableMap<String, Any?> = TreeMap()
        treeMap["version"] = version
        treeMap["encType"] = "SM4"
        treeMap["signType"] = "SM2"
        treeMap["appId"] = appId
        treeMap["timestamp"] = Date().time
        treeMap["extra"] = null as Any?
        treeMap["data"] = JSON.toJSON(dataMap)
        val signStr = CommonUtil.getSignStr(secret, treeMap)
        val signData = sm2.sign(signStr, privateKeys)
        treeMap["signData"] = signData
        LOG.debug("签名结果：$signData")
        val encDataBye = SM4Util.encrypt_Ecb_Padding(encryptKeys, treeMap["data"].toString().toByteArray())
        val encData = Hex.encodeStr(encDataBye)
        treeMap["encData"] = encData
        LOG.debug("加密结果：$encData")
        treeMap.keys.removeIf { it == "data" }
        return JSON.toJSONString(treeMap)
    }

    fun processRspData(rspData: String?): String {
        var treeMap: MutableMap<String, Any?> = mutableMapOf()
        treeMap = JSON.parseObject(rspData, treeMap.javaClass)

        val codeObj = treeMap["code"] as Int?
        if (codeObj != null && codeObj == 0) {
            val encData = treeMap["encData"].toString()
            val jsonStr: String? = try {
                val cipherData = Hex.decode(encData)
                val cipherByte = SM4Util.decrypt_Ecb_Padding(encryptKeys, cipherData)
                val dEncData = String(cipherByte)
                val jsonObject = org.json.JSONObject(dEncData)
                CommonUtil.valueToString(jsonObject)
            } catch (e: Exception) {
                throw ServerException(FuncRetCode.ERROR_CODE_ENCRYPT_ERROR)
            }
            var dataMap: Map<String, Any?> = TreeMap()
            dataMap = JSON.parseObject(jsonStr, dataMap.javaClass)

            treeMap["data"] = dataMap
            val signStr = CommonUtil.getSignStr(secret, treeMap)
            if (!sm2.verify(signStr, treeMap["signData"].toString(), publicKeys)) {
                throw ServerException(FuncRetCode.ERROR_CODE_SIGN_ERROR)
            }
            treeMap.keys.removeIf { it in setOf("encData", "signData") }
        }
        return JSON.toJSONString(treeMap)
    }
}