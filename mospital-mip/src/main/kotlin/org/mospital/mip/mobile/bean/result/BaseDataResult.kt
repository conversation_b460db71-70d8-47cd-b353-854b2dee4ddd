package org.mospital.mip.mobile.bean.result

import com.fasterxml.jackson.annotation.JsonIgnore


/**
 * 基础请求类
 */
open class BaseDataResult<T> {

    /**
     * 响应状态码
     */
    var code: Int = -1

    /**
     * 渠道id
     */
    var appId: String = ""

    /**
     * 当前时间
     */
    var timestamp: String = ""

    /**
     * 加密方式
     */
    var encType: String = ""

    /**
     * 签名方式
     */
    var signType: String = ""

    /**
     * 签名串
     */
    var signData: String = ""

    /**
     * 加密数据
     */
    var encData: String = ""

    /**
     * 响应异常信息
     */
    var message: String? = null

    /**
     * 响应标识
     */
    var success: Boolean? = false

    /**
     * 类型
     */
    var type: String? = null

    /**
     * 额外参数
     */
    var extra: String? = null

    /**
     * 响应数据
     */
    var data: T? = null

    @get:JsonIgnore
    val isOk: Boolean
        get() = 0 == code

}