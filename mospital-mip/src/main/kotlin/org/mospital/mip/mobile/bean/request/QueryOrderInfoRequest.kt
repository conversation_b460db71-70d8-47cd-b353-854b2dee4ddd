package org.mospital.mip.mobile.bean.request

/**
 * 5.3.1 医保订单结算结果查询 请求类
 */
class QueryOrderInfoRequest {

    /**
     * 支付订单号。
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var payOrdId: String = ""

    /**
     * 定点机构编码。
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var orgCodg: String = ""

    /**
     * 支付 token。与费用上传时一致
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var payToken: String = ""

    /**
     * 证件号码。
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var idNo: String = ""

    /**
     * 用户姓名。
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var userName: String = ""

    /**
     * 证件类别。字典人员证件类型(psn_cert_type)
     * 参数类型：字符型(3)
     * 是否必填：Y
     */
    var idType: String = ""

    /**
     * 扩展数据。可参考 FSI 的接口要求
     * 参数类型：字符型(4000)
     * 是否必填：N
     */
    var expContent: String = ""

}