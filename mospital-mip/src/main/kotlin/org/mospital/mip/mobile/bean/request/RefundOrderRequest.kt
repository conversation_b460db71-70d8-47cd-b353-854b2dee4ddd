package org.mospital.mip.mobile.bean.request

/**
 * 5.2.3 医保退费 请求类
 */
class RefundOrderRequest {

    /**
     * 支付订单号。处方上传的出参订单号
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var payOrdId: String = ""

    /**
     * 应用退款流水号。应用退费流水号
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var appRefdSn: String = ""

    /**
     * 应用退费时间。yyyyMMddHHmmss
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var appRefdTime: String = ""

    /**
     * 总退费金额。原交易记录总金额
     * 参数类型：数值型(12,2)
     * 是否必填：Y
     */
    var totlRefdAmt: String = ""

    /**
     * 医保个人账户支付。
     * 参数类型：数值型(12,2)
     * 是否必填：N
     */
    var psnAcctRefdAmt: String = ""

    /**
     * 基金支付。含商保
     * 参数类型：数值型(12,2)
     * 是否必填：N
     */
    var fundRefdAmt: String = ""

    /**
     * 现金退费金额。
     * 参数类型：数值型(12,2)
     * 是否必填：Y
     */
    var cashRefdAmt: String = ""

    /**
     * 电子凭证授权 Token。
     * 参数类型：字符型(64)
     * 是否必填：Y
     */
    var ecToken: String = ""

    /**
     * 退费类型。ALL:全部，CASH:只退现金 HI:只退医保
     * 参数类型：字符型(40)
     * 是否必填：Y
     */
    var refdType: String = ""

    /**
     * 扩展数据。可参考 FSI 的接口要求
     * 参数类型：字符型(4000)
     * 是否必填：N
     */
    var expContent: String = ""

    /**
     * payAuthNo 支付授权码。与 ecToken 不可同时为空
     * 参数类型：字符型(40)
     * 是否必填：N
     */
    var payAuthNo: String = ""


}