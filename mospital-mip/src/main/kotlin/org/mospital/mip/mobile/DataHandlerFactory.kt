package org.mospital.mip.mobile

import org.mospital.mip.FsiRootConfigLoader
import java.util.concurrent.ConcurrentHashMap

object DataHandlerFactory {

    private val configMap = ConcurrentHashMap<String, DataHandler>()

    val serviceUrl: String get() = FsiRootConfigLoader.config.mobilePayUrl

    private fun createDataHandler(configName: String): DataHandler {
        val fsiConfig = FsiRootConfigLoader.config
        val wechatConfig = when (configName.lowercase()) {
            "wechat" -> fsiConfig.wechat
            // "alipay" -> fsiConfig.alipay // Assuming alipay might be a future group in fsi.yaml
            else -> throw IllegalArgumentException("DataHandlerFactory: Configuration for '$configName' not found in fsi.yaml")
        }

        return DataHandler(
            wechatConfig.appId,
            wechatConfig.sm4Key,
            wechatConfig.platformPublicKey,
            wechatConfig.channelPrivateKey,
        ).apply {
            version = "2.0.1" // This seems hardcoded, leaving as is.
        }
    }

    fun getConfig(configName: String): DataHandler {
        return configMap.computeIfAbsent(configName) { createDataHandler(configName) }
    }

    val wechat: DataHandler by lazy { getConfig("wechat") }
    val alipay: DataHandler by lazy { getConfig("alipay") } // This will likely fail if no "alipay" section in fsi.yaml and FsiConfig
}