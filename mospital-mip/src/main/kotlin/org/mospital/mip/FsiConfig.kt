package org.mospital.mip

/**
 * FsiConfig 配置类
 * 对应 fsi.yaml 配置文件
 */
data class FsiConfig(
    val mobilePayUrl: String = "",
    val wechat: WechatFsiConfig = WechatFsiConfig()
)

data class WechatFsiConfig(
    val appId: String = "",
    val sm4Key: String = "",
    val channelPrivateKey: String = "",
    val platformPublicKey: String = "",
    val orgName: String = "",
    val orgCodg: String = "",
    val orgChnlCrtfCodg: String = ""
)
