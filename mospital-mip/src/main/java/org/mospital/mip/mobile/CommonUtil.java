package org.mospital.mip.mobile;

import java.util.ArrayList;
import java.util.List;

public class CommonUtil {

    private CommonUtil() {
    }

    public static List<String> getStrList(String inputString, int length, int size) {
        List<String> list = new ArrayList<>();

        for (int index = 0; index < size; ++index) {
            String childStr = inputString.substring(index * length, Math.min((index + 1) * length, inputString.length()));
            list.add(childStr);
        }

        return list;
    }

}
