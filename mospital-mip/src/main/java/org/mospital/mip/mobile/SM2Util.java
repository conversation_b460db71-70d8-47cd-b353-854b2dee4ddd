package org.mospital.mip.mobile;

import org.dromara.hutool.log.Log;
import org.bouncycastle.asn1.gm.GMNamedCurves;
import org.bouncycastle.asn1.gm.GMObjectIdentifiers;
import org.bouncycastle.asn1.x9.X9ECParameters;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPrivateKey;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPublicKey;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.jce.spec.ECParameterSpec;
import org.bouncycastle.jce.spec.ECPrivateKeySpec;
import org.bouncycastle.jce.spec.ECPublicKeySpec;
import org.bouncycastle.math.ec.ECPoint;
import org.bouncycastle.util.encoders.Hex;

import java.math.BigInteger;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.util.Base64;
import java.util.List;

public class SM2Util {

    private BouncyCastleProvider provider;
    private X9ECParameters parameters;
    private ECParameterSpec ecParameterSpec;
    private KeyFactory keyFactory;
    Base64.Decoder decoder = Base64.getDecoder();

    public SM2Util() {
        try {
            this.provider = new BouncyCastleProvider();
            this.parameters = GMNamedCurves.getByName("sm2p256v1");
            this.ecParameterSpec = new ECParameterSpec(this.parameters.getCurve(), this.parameters.getG(), this.parameters.getN(), this.parameters.getH());
            this.keyFactory = KeyFactory.getInstance("EC", this.provider);
        } catch (Exception e) {
            Log log = Log.get();
            log.error(e.getMessage(), e);
        }

    }

    public String sign(String plainText, String prvKey) throws NoSuchAlgorithmException, InvalidKeySpecException, InvalidKeyException, SignatureException {
        Signature signature = Signature.getInstance(GMObjectIdentifiers.sm2sign_with_sm3.toString(), this.provider);
        BigInteger bigInteger = new BigInteger(prvKey, 16);
        BCECPrivateKey privateKey = (BCECPrivateKey) this.keyFactory.generatePrivate(new ECPrivateKeySpec(bigInteger, this.ecParameterSpec));
        signature.initSign(privateKey);
        signature.update(plainText.getBytes());
        String signStr = Base64.getEncoder().encodeToString(signature.sign());
        signStr = (new BigInteger(this.decoder.decode(signStr))).toString(16);
        String derStr = com.tencent.mip.util.SM2Util.SM2SignAsn1.parseSm2SignAsn1Object(signStr);
        byte[] hexStr = Hex.decode(derStr);
        return Base64.getEncoder().encodeToString(hexStr);
    }

    public boolean verify(String plainText, String signatureValue, String pubKey) throws NoSuchAlgorithmException {
        Signature signature = Signature.getInstance(GMObjectIdentifiers.sm2sign_with_sm3.toString(), this.provider);

        try {
            ECPoint ecPoint = this.parameters.getCurve().decodePoint(Hex.decode(pubKey));
            BCECPublicKey key = (BCECPublicKey) this.keyFactory.generatePublic(new ECPublicKeySpec(ecPoint, this.ecParameterSpec));
            signature.initVerify(key);
            signature.update(plainText.getBytes());
            byte[] signDataByte = Base64.getDecoder().decode(signatureValue);
            String sigDataHex = Hex.toHexString(signDataByte);
            List<String> list = CommonUtil.getStrList(sigDataHex, 64, 2);
            String sm2SignAsn1 = com.tencent.mip.util.SM2Util.SM2SignAsn1.buildSm2SignAsn1Object(new BigInteger(list.get(0), 16), new BigInteger(list.get(1), 16));
            byte[] sm2SignAsn1Byte = Hex.decode(sm2SignAsn1);
            String sm2SignAsn1ByteText = Base64.getEncoder().encodeToString(sm2SignAsn1Byte);
            return signature.verify(Base64.getDecoder().decode(sm2SignAsn1ByteText));
        } catch (Exception var13) {
            return false;
        }
    }

}
