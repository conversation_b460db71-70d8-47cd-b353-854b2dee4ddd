package org.mospital.mip.mobile.bean.request

import org.junit.jupiter.api.Test
import org.mospital.mip.mobile.MobilePayService

class MobilePayServiceTest {

    @Test
    fun testQueryOrder() {
        MobilePayService.queryWechatOrder(QueryOrderInfoRequest().apply {
            this.payOrdId = "ORD650100202404291252340024868"
            this.orgCodg = "H65402500002"
            this.payToken = "TSN650100202404291252342063841"
            this.idNo = "654125197701102675"
            this.userName = "张涛"
            this.idType = "01"
            this.expContent = ""
        })
    }

}