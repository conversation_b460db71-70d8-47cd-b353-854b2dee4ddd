package org.mospital.shine.checkin.ws

import com.fasterxml.jackson.annotation.JsonAlias

data class CheckInResponse(
    @JsonAlias("ResultCode", "code")
    val code: String,
    @JsonAlias("ResultMsg", "message")
    val message: String,
    val data: CheckInResult?
) {
    fun isOk(): Boolean = code == "0"

    data class CheckInResult(
        @JsonAlias("queue_id", "queueId")
        val queueId: String,
        @JsonAlias("patient_name", "patientName")
        val patientName: String,
        @JsonAlias("queue_type_source_id", "queueTypeSourceId")
        val queueTypeSourceId: String,
        @JsonAlias("queue_type_name", "queueTypeName")
        val queueTypeName: String,
        @JsonAlias("doctor_id", "doctorId")
        val doctorId: String,
        @<PERSON>sonAlias("doctor_name", "doctorName")
        val doctorName: String,
        @JsonAlias("reserve_time", "reserveTime")
        val reserveTime: String,
        @JsonAlias("reserve_end_time", "reserveEndTime")
        val reserveEndTime: String,
        @JsonAlias("queue_state", "queueState")
        val queueState: String,
        @JsonAlias("register_id", "registerId")
        val registerId: String,
        @JsonAlias("triage_name", "triageName")
        val triageName: String,
        @JsonAlias("wait_count", "waitCount")
        val waitCount: Int
    )
}