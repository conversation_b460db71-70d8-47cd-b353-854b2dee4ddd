package org.mospital.shine.checkin.ws

/**
 * ShineCheckinWsConfig 配置类
 * 对应 shine-checkin-ws.yaml 配置文件
 */
data class ShineCheckinWsConfig(
    val shineCheckin: ShineCheckinServiceConfig = ShineCheckinServiceConfig()
)

data class ShineCheckinServiceConfig(
    val url: String = "",
    val tag: ShineCheckinTagConfig = ShineCheckinTagConfig()
)

data class ShineCheckinTagConfig(
    val sinput: String = "",
    val getQueueList: String = "", // Matches YAML key
    val checkIn: String = ""       // Matches YAML key
)
