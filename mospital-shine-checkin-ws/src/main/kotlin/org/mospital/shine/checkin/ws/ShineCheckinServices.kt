package org.mospital.shine.checkin.ws

import org.dromara.hutool.core.lang.Singleton
import org.dromara.hutool.http.client.engine.ClientEngine
import org.dromara.hutool.http.client.engine.ClientEngineFactory
import org.dromara.hutool.http.webservice.SoapClient
import org.mospital.common.IdUtil
import org.mospital.jackson.ConfigLoader
import org.mospital.jackson.JacksonKit
import org.slf4j.Logger
import org.slf4j.LoggerFactory

// Helper object to load configuration
internal object ShineCheckinWsConfigLoader {
    val config: ShineCheckinWsConfig by lazy {
        ConfigLoader.loadConfig("shine-checkin-ws.yaml", ShineCheckinWsConfig::class.java, ShineCheckinWsConfigLoader::class.java)
    }
}

object ShineCheckinServices {

    private val log: Logger = LoggerFactory.getLogger(ShineCheckinServices::class.java)

    init {
        // 强制使用 OkHttp 引擎
        val okHttpEngine = ClientEngineFactory.createEngine("OkHttp")
        Singleton.put(ClientEngine::class.java.name, okHttpEngine)
    }

    private fun newClient(methodName: String, inputXml: String): SoapClient {
        return SoapClient.of(ShineCheckinWsConfigLoader.config.shineCheckin.url)
            .setMethod("tem:$methodName", "http://tempuri.org/")
            .setParam(ShineCheckinWsConfigLoader.config.shineCheckin.tag.sinput.ifEmpty { "SInput" }, "<![CDATA[$inputXml]]>")
    }

    private fun sendRequest(methodName: String, inputXml: String, resultTag: String = "${methodName}Result"): String {
        val traceId: String = IdUtil.simpleUUID()

        log.debug("Request#$traceId: $inputXml")

        val responseText: String = newClient(methodName, inputXml).send().bodyText
        log.debug("Response#$traceId: $responseText")

        return responseText.substringAfter("<$resultTag>").substringBefore("</$resultTag>")
    }

    /**
     * 获取当前诊区下挂号记录
     * @param searchCode 患者医保卡、身份证、就诊卡
     */
    fun getQueueList(searchCode: String): QueueListResponse {
        val inputXml = """
            <data>
                <searchCode>$searchCode</searchCode>
                <triageIP></triageIP>
            </data>
        """.trimIndent()
        // Original code used "shine.tag.GetQueueList", now it's shineCheckin.tag.getQueueList
        val methodName = ShineCheckinWsConfigLoader.config.shineCheckin.tag.getQueueList.ifEmpty { "GetQueueList" }
        val responseJson = sendRequest(methodName, inputXml)
        return JacksonKit.readValue(responseJson, QueueListResponse::class.java)
    }

    /**
     * 签到
     */
    fun checkIn(queueId: String): CheckInResponse {
        val inputXml = """
            <data>
                <queue_id>$queueId</queue_id>
            </data>
        """.trimIndent()
        // Original code used "shine.tag.CheckIn", now it's shineCheckin.tag.checkIn
        val methodName = ShineCheckinWsConfigLoader.config.shineCheckin.tag.checkIn.ifEmpty { "CheckIn" }
        val responseJson = sendRequest(methodName, inputXml)
        return JacksonKit.readValue(responseJson, CheckInResponse::class.java)
    }

}
