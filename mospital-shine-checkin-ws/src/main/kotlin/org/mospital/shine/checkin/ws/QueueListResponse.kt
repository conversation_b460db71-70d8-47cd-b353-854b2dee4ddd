package org.mospital.shine.checkin.ws

import com.fasterxml.jackson.annotation.JsonAlias
import org.mospital.common.PatientDataToken
import org.mospital.jackson.JacksonKit

data class QueueListResponse(
    @JsonAlias("ResultCode", "code")
    val code: String,
    @JsonAlias("ResultMsg", "message")
    val message: String,
    val data: List<QueueInfo>?
) {
    fun isOk(): Boolean = code == "0"

    data class QueueInfo(
        @JsonAlias("queue_id", "queueId")
        val queueId: String,
        @JsonAlias("patient_id", "patientId")
        val patientId: String,
        @JsonAlias("patient_name", "patientName")
        val patientName: String,
        @JsonAlias("register_id", "registerId")
        val registerId: String,
        @JsonAlias("queue_type_source_id", "queueTypeSourceId")
        val queueTypeSourceId: String,
        @JsonAlias("queue_type_name", "queueTypeName")
        val queueTypeName: String,
        @JsonAlias("patient_state_dm", "patientStateCode")
        val patientStateCode: Int,
        @JsonAlias("patient_state_mc", "patientStateName")
        val patientStateName: String,
        @JsonAlias("doctor_id", "doctorId")
        val doctorId: String,
        @JsonAlias("doctor_name", "doctorName")
        val doctorName: String,
        @JsonAlias("reserve_time", "reserveTime")
        val reserveTime: String,
        @JsonAlias("reserve_end_time", "reserveEndTime")
        val reserveEndTime: String,
        @JsonAlias("call_max", "callMax")
        val callMax: String,
        @JsonAlias("triage_name", "triageName")
        val triageName: String,
        @JsonAlias("wait_count", "waitCount")
        val waitCount: Int,
    ) {
        val token: String
            get() = PatientDataToken(dataId = queueId, patientId = patientId).generateToken()

        /**
         * 是否能签到
         */
        var canCheckIn: Boolean = patientStateName in arrayOf("未报到", "未诊", "过号", "诊结")
    }
}