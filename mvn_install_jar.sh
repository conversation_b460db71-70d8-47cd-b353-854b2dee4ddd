#!/usr/bin/env bash
# https://open.tengmed.com/user/openAccess/tools?moduleId=39
mvn install:install-file -Dfile=lib/tencent-health-card-sdk-v2.9.jar -DgroupId=com.tencent.healthcard -DartifactId=open-platform-sdk -Dversion=2.9-jdk1.8 -Dpackaging=jar
mvn install:install-file -Dfile=lib/tencent-health-card-sdk-v3.0.jar -DgroupId=com.tencent.healthcard -DartifactId=open-platform-sdk -Dversion=3.0-jdk1.8 -Dpackaging=jar
mvn install:install-file -Dfile=lib/tencent-med-request-data-sdk-2.0.3.jar -DgroupId=com.tencent.med -DartifactId=request-data-sdk -Dversion=2.0.3 -Dpackaging=jar
mvn install:install-file -Dfile=lib/ehcsdk-java-sdk.jar -DgroupId=com.ylzinfo.ehc -DartifactId=erhc-sdk -Dversion=1.0.0 -Dpackaging=jar
mvn install:install-file -Dfile=lib/bocom-openapi-sdk-1.1.jar -DgroupId=com.bocom -DartifactId=bocom-sdk -Dversion=1.1.0 -Dpackaging=jar
