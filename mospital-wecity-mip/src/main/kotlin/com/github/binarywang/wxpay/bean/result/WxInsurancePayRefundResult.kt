package com.github.binarywang.wxpay.bean.result

import com.thoughtworks.xstream.annotations.XStreamAlias
import org.w3c.dom.Document

@XStreamAlias("xml")
class WxInsurancePayRefundResult : BaseWxPayResult(), java.io.Serializable {

    companion object {
        private const val serialVersionUID: Long = 580545539882031629L
    }

    /**
     * 订单总金额
     */
    @XStreamAlias("total_fee")
    var totalFee: Int = 0

    /**
     * 现金退款金额
     */
    @XStreamAlias("cash_refund_fee")
    var cashRefundFee: Int = 0

    /**
     * 医保退款金额（目前只能全额退）
     */
    @XStreamAlias("insurance_refund_fee")
    var insuranceRefundFee: Int = 0

    /**
     * 微信生成的退款医疗订单号，后续可以根据此退款订单号查询退款单
     */
    @XStreamAlias("med_refund_id")
    var medRefundId: String = ""

    /**
     * 微信医保支付生成的微信支付退款订单号
     */
    @XStreamAlias("cash_refund_id")
    var cashRefundId: String = ""

    /**
     * 微信医保支付生成的医保退款单号
     */
    @XStreamAlias("insurance_refund_id")
    var insuranceRefundId: String = ""

    override fun loadXml(d: Document) {
        totalFee = readXmlInteger(d, "total_fee") ?: 0
        cashRefundFee = readXmlInteger(d, "cash_refund_fee") ?: 0
        insuranceRefundFee = readXmlInteger(d, "insurance_refund_fee") ?: 0
        medRefundId = readXmlString(d, "med_refund_id") ?: ""
        cashRefundId = readXmlString(d, "cash_refund_id") ?: ""
        insuranceRefundId = readXmlString(d, "insurance_refund_id") ?: ""
    }

}
