package com.github.binarywang.wxpay.bean.request

import com.thoughtworks.xstream.annotations.XStreamAlias
import me.chanjar.weixin.common.annotation.Required

@XStreamAlias("xml")
class WxInsurancePayDownloadBillRequest(): BaseWxPayRequest() {
    companion object {
        private const val serialVersionUID: Long = -2813485637452274168L
    }

    /**
     * 对账单日期，格式为yyyyMMdd
     */
    @Required
    @XStreamAlias("bill_date")
    private var billDate: String = ""

    /**
     * 帐单类型
     * ALL：返回当日所有订单信息，默认值
     * SUCCESS：返回当日成功支付的订单
     * REFUND：返回当日退款订单
     */
    @XStreamAlias("bill_type")
    private var billType: String = "ALL"

    constructor(billDate: String, billType: String = "ALL"): this() {
        this.billDate = billDate
        this.billType = billType
    }

    override fun checkConstraints() {

    }

    override fun storeMap(map: MutableMap<String, String>) {
        map["bill_date"] = billDate
        map["bill_type"] = billType
    }
}