package com.github.binarywang.wxpay.service

import com.github.binarywang.wxpay.bean.request.*
import com.github.binarywang.wxpay.bean.result.*
import com.github.binarywang.wxpay.config.WxPayConfig
import com.github.binarywang.wxpay.constant.WxPayConstants
import com.github.binarywang.wxpay.exception.WxPayException
import com.github.binarywang.wxpay.util.SignUtils
import com.github.binarywang.wxpay.util.XmlConfig
import com.google.common.collect.Lists
import me.chanjar.weixin.common.api.WxConsts
import org.apache.commons.lang3.StringUtils
import org.slf4j.LoggerFactory
import java.util.*

class WxInsurancePayService(val config: WxPayConfig, val accessTokenProvider: (Boolean) -> String, val wxPayService: WxPayService) {

    init {
        XmlConfig.fastMode = true
    }

    companion object {
        const val BASE_URL = "https://api.weixin.qq.com"
        private val logger = LoggerFactory.getLogger(WxInsurancePayService::class.java)
    }

    /**
     * 挂号/诊间支付统一下单
     */
    @Throws(WxPayException::class)
    fun unifiedOrder(request: WxInsurancePayUnifiedOrderRequest): WxInsurancePayUnifiedOrderResult {
        return execute("/payinsurance/unifiedorder", request, false, WxInsurancePayUnifiedOrderResult::class.java)
    }

    /**
     * 查询支付单
     */
    @Throws(WxPayException::class)
    fun queryOrder(request: WxInsurancePayOrderQueryRequest): WxInsurancePayOrderQueryResult {
        return execute("/payinsurance/queryorder", request, false, WxInsurancePayOrderQueryResult::class.java)
    }

    /**
     * 查询支付单
     * @param medTransId 微信生成的医疗订单号，与hosp_out_trade_no二选一，建议使用med_trans_id
     * @param hospOutTradeNo 第三方服务商订单号，与med_trans_id二选一，建议使用med_trans_id
     */
    @Throws(WxPayException::class)
    fun queryOrder(medTransId: String, hospOutTradeNo: String): WxInsurancePayOrderQueryResult {
        val request = WxInsurancePayOrderQueryRequest(medTransId, hospOutTradeNo)
        return queryOrder(request)
    }

    /**
     * 退款
     */
    @Throws(WxPayException::class)
    fun refund(request: WxInsurancePayRefundRequest): WxInsurancePayRefundResult {
        return execute("/payinsurance/refund", request, true, WxInsurancePayRefundResult::class.java)
    }

    /**
     * 查询退款
     */
    @Throws(WxPayException::class)
    fun queryRefund(request: WxInsurancePayRefundQueryRequest): WxInsurancePayRefundQueryResult {
        return execute("/payinsurance/queryrefund", request, false, WxInsurancePayRefundQueryResult::class.java)
    }

    /**
     * 下载对帐单
     */
    @Throws(WxPayException::class)
    fun downloadBill(request: WxInsurancePayDownloadBillRequest): WxInsurancePayDownloadBillResult {
        return execute("/payinsurance/billdownload", request, false, WxInsurancePayDownloadBillResult::class.java)
    }

    /**
     * 关闭订单
     */
    @Throws(WxPayException::class)
    fun closeOrder(request: WxInsurancePayCloseOrderRequest): WxInsurancePayCloseOrderResult {
        return execute("/payinsurance/closeorder", request, false, WxInsurancePayCloseOrderResult::class.java)
    }

    private fun appendAccessTokenToUrl(url: String): String {
        val accessToken: String = accessTokenProvider.invoke(false)
        return "$BASE_URL$url?access_token=$accessToken"
    }

    @Throws(WxPayException::class)
    private fun <T : BaseWxPayResult> execute(
        url: String,
        request: BaseWxPayRequest,
        useKey: Boolean,
        resultClass: Class<T>,
    ): T {
        try {
            request.checkAndSign(this.config)

            val responseContent = wxPayService.post(appendAccessTokenToUrl(url), request.toXML(), useKey)
            val result = BaseWxPayResult.fromXML(responseContent, resultClass)
            checkResult(result, request.signType, true)
            return result
        } catch (e: WxPayException) {
            logger.debug(e.message, e)
            if (e.errCode?.toInt() in WxConsts.ACCESS_TOKEN_ERROR_CODES) {
                accessTokenProvider.invoke(true)
            }
            throw e
        }
    }

    /**
     * 校验返回结果的签名
     *
     */
    @Throws(WxPayException::class)
    private fun checkResult(result: BaseWxPayResult, signType: String?, checkSuccess: Boolean) {
        val map = result.toMap()
        if (result.sign != null && !SignUtils.checkSign(map, signType, config.mchKey)) {
            logger.debug("校验结果签名失败，类型：{}，参数：{}", result.javaClass.canonicalName, map)
            throw WxPayException("参数格式校验错误！")
        }


        //校验结果是否成功
        if (checkSuccess) {
            val successStrings: List<String> = Lists.newArrayList(WxPayConstants.ResultCode.SUCCESS, "")
            if (
                !successStrings.contains(StringUtils.trimToEmpty(result.resultCode).uppercase(Locale.getDefault()))
                ||
                !successStrings.contains(StringUtils.trimToEmpty(result.getReturnCode()).uppercase(Locale.getDefault()))
            ) {
                val errorMsg = StringBuilder()
                if (result.getReturnCode() != null) {
                    errorMsg.append("返回代码：").append(result.getReturnCode())
                }
                if (result.getReturnMsg() != null) {
                    errorMsg.append("，返回信息：").append(result.getReturnMsg())
                }
                if (result.resultCode != null) {
                    errorMsg.append("，结果代码：").append(result.resultCode)
                }
                if (result.errCode != null) {
                    errorMsg.append("，错误代码：").append(result.errCode)
                }
                if (result.errorCode != null) {
                    errorMsg.append("，错误代码：").append(result.errorCode)
                }
                if (result.errCodeDes != null) {
                    errorMsg.append("，错误详情：").append(result.errCodeDes)
                }
                logger.error("\n结果业务代码异常，返回结果：{},\n{}", map, errorMsg.toString())
                throw WxPayException.from(result)
            }
        }
    }
}
