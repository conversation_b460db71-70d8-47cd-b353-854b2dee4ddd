package com.github.binarywang.wxpay.bean.result

import com.thoughtworks.xstream.annotations.XStreamAlias
import org.w3c.dom.Document

@XStreamAlias("xml")
class WxInsurancePayCloseOrderResult : BaseWxPayResult(), java.io.Serializable {

    companion object {
        private const val serialVersionUID: Long = 3844128515879688471L
    }

    override fun loadXml(d: Document) {
        // do nothing
    }

}