package com.github.binarywang.wxpay.bean.notify

import com.github.binarywang.wxpay.util.SignUtils

class WxInsuranceOrderNotifyResponse {

    companion object {
        private const val serialVersionUID: Long = -1L

        fun success(returnMsg: String = "OK", signKey: String): WxInsuranceOrderNotifyResponse {
            val response = WxInsuranceOrderNotifyResponse()
            response.resultCode = "SUCCESS"
            response.returnMsg = returnMsg
            response.signKey = signKey
            response.sign = response.sign()
            return response
        }

        fun fail(returnMsg: String, signKey: String): WxInsuranceOrderNotifyResponse {
            val response = WxInsuranceOrderNotifyResponse()
            response.resultCode = "FAIL"
            response.returnMsg = returnMsg
            response.signKey = signKey
            response.sign = response.sign()
            return response
        }

    }

    private val returnCode: String = "SUCCESS"

    var returnMsg: String = ""

    var resultCode: String = ""

    var signKey: String = ""

    private val nonceStr: String = System.currentTimeMillis().toString()

    private var sign: String = ""

    fun sign(): String {
        return SignUtils.createSign(
            mapOf(
                "return_code" to returnCode,
                "return_msg" to returnMsg,
                "result_code" to resultCode,
                "nonce_str" to nonceStr
            ),
            "MD5",
            signKey,
            emptyArray()
        )
    }

    fun toXML(): String {
        return """
            <xml>
                <return_code><![CDATA[$returnCode]]></return_code>
                <return_msg><![CDATA[$returnMsg]]></return_msg>
                <result_code><![CDATA[$resultCode]]></result_code>
                <nonce_str><![CDATA[$nonceStr]]></nonce_str>
                <sign><![CDATA[$sign]]></sign>
            </xml>
        """.trimIndent()
    }

}