package com.github.binarywang.wxpay.bean.request

import com.github.binarywang.wxpay.config.WxPayConfig
import com.thoughtworks.xstream.annotations.XStreamAlias
import com.thoughtworks.xstream.annotations.XStreamOmitField
import me.chanjar.weixin.common.annotation.Required

@XStreamAlias("xml")
class WxInsurancePayUnifiedOrderRequest() : BaseWxPayRequest() {

    companion object {
        private const val serialVersionUID: Long = -6090555722702311850L
    }

    /**
     * 支付类型
     * RegPay=挂号支付
     * MedPay=药费支付
     * DiagPay=诊间支付
     * InHospPay=住院费支付
     * PharmacyPay=药店支付
     * InsurancePay=保险费支付
     * IntRegPay=互联网医院挂号支付
     * IntReDiagPay=互联网医院复诊支付
     * IntPscPay=互联网医院处方支付
     * CovidExamPay=新冠检测费用
     * CvidAntigenPay=新冠抗原检测
     */
    @Required
    @XStreamAlias("order_type")
    var orderType: String = ""

    /**
     * 用户标识
     */
    @XStreamAlias("openid")
    var openid: String = ""

    /**
     * 用户子标识
     */
    @XStreamAlias("sub_openid")
    var subOpenid: String = ""

    /**
     * 第三方服务商订单号
     */
    @Required
    @XStreamAlias("hosp_out_trade_no")
    var hospOutTradeNo: String = ""

    /**
     * 医院名称
     */
    @Required
    @XStreamAlias("hospital_name")
    var hospitalName: String = ""

    /**
     * 总共需要支付现金金额，对应医保移动支付中心支付下单接口的出参 feeSumamt
     */
    @Required
    @XStreamAlias("total_fee")
    var totalFee: Int = 0

    /**
     * 现金需要支付的金额
     * 单位为分>=0，线上预结算模式金额填0，线下预结算模式填实际自费金额
     * 对应医保移动支付中心支付下单接口的出参 ownPayAmt
     */
    @Required
    @XStreamAlias("cash_fee")
    var cashFee: Int = 0

    /**
     * 医保支付金额，单位为分>=0
     * 医保基金支付+个人账户支出，对应医保移动支付中心支付下单接口的出参 fundPay + psnAcctPay
     */
    @Required
    @XStreamAlias("insurance_fee")
    var insuranceFee: Int = 0

    /**
     * 由医疗机构额外增加的现金金额
     */
    @XStreamAlias("cash_add_fee")
    var cashAddFee: Int = 0

    /**
     * 额外现金金额文案
     */
    @XStreamAlias("cash_add_wording")
    var cashAddWording: String = ""

    /**
     * 由医院减免的现金优惠金额
     */
    @XStreamAlias("cash_reduced_fee")
    var cashReducedFee: Int = 0

    /**
     * 减免金额文案
     */
    @XStreamAlias("cash_reduced_wording")
    var cashReducedWording: String = ""

    /**
     * 是否允许预结算费用发生变化
     */
    @Required
    @XStreamAlias("allow_fee_change")
    var allowFeeChange: Int = 0

    /**
     * 用户端ip
     */
    @Required
    @XStreamAlias("spbill_create_ip")
    var spbillCreateIp: String = ""

    /**
     * 回调url
     */
    @Required
    @XStreamAlias("notify_url")
    var notifyUrl: String = ""

    /**
     * no_credit--指定不能使用信用卡支付
     */
    @XStreamAlias("limit_pay")
    var limitPay: String = ""

    /**
     * 商品描述
     */
    @Required
    @XStreamAlias("body")
    var body: String = ""

    /**
     * 支付后回跳的页面，不论成功或者失败均会回跳
     */
    @Required
    @XStreamAlias("return_url")
    var returnUrl: String = ""

    /**
     * 支付方式
    1：现金 2：医保 3：现金+医保
    备注说明：
    1、当订单要走医保支付时，
    （1）cash_fee>0, paytype填3
    （2）cash_fee=0, paytype填2
    2、当订单不使用医保支付时 paytype填1。
    3、当订单有走医保【6201】相关接口时，即使订单为纯自费订单，paytype填3。
     */
    @Required
    @XStreamAlias("pay_type")
    var payType: Int = 3

    /**
     * 城市ID
     */
    @Required
    @XStreamAlias("city_id")
    var cityId: String = ""

    /**
     * 医保部分扣费类型
    默认为 0：统筹+个账
    1：个账
    2：统筹
     */
    @XStreamAlias("consume_type")
    var consumeType: Int = 0

    /**
     * 证件类型
    1 居民身份证
    9 户口本
    8 外国人护照
    5 台湾居民来往大陆通行证
    4 澳门居民往来内地通行证
    6 香港居民往来内地通行证
    7 外国人永久居留证
     */
    @Required
    @XStreamAlias("user_card_type")
    var userCardType: Int = 1

    /**
     * 证件号码
     */
    @Required
    @XStreamAlias("user_card_no")
    var userCardNo: String = ""

    /**
     * 真实姓名
     */
    @Required
    @XStreamAlias("user_name")
    var userName: String = ""

    /**
     * 医保标识
     */
    @Required
    @XStreamAlias("is_dept")
    var isDept: String = "4"

    /**
     * 医院HIS系统订单号
     */
    @Required
    @XStreamAlias("serial_no")
    var serialNo: String = ""

    /**
     * 医疗机构编码（医保局分配给机构）
     */
    @Required
    @XStreamAlias("org_no")
    var orgNo: String = ""

    /**
     * 医院下单时间，格式为yyyyMMddHHmmss
     */
    @Required
    @XStreamAlias("gmt_out_create")
    var gmtOutCreate: String = ""

    /**
     * 参考医保结构体（医疗机构透传医保）
     */
    @Required
    @XStreamAlias("request_content")
    var requestContent: String = ""

    /**
     * 医保支付授权码
     */
    @Required
    @XStreamOmitField
    var payAuthNo: String = ""

    /**
     * 向医保上传费用明细时产生的支付订单号
     */
    @Required
    @XStreamOmitField
    var payOrderId: String = ""

    /**
     * 用户地理定位信息，经度
     */
    @Required
    @XStreamOmitField
    var longitude: String = "0"

    /**
     * 用户地理定位信息，纬度
     */
    @Required
    @XStreamOmitField
    var latitude: String = "0"

    /**
     * 业务单据号
     */
    @XStreamAlias("bill_no")
    var billNo: String = ""

    /**
     * 扩展字段
     */
    @XStreamAlias("extends")
    var extends: String = ""

    /**
     * 附加数据
     */
    @XStreamAlias("attach")
    var attach: String = ""

    /**
     * 渠道号
     */
    @Required
    @XStreamAlias("channel_no")
    var channelNo: String = ""

    override fun checkConstraints() {

    }

    override fun getIgnoredParamsForSign(): Array<String> {
        return arrayOf("payAuthNo", "payOrderId", "longitude", "latitude")
    }

    override fun storeMap(map: MutableMap<String, String>) {
        map["order_type"] = orderType
        map["openid"] = openid
        map["sub_openid"] = subOpenid
        map["hosp_out_trade_no"] = hospOutTradeNo
        map["hospital_name"] = hospitalName
        map["total_fee"] = totalFee.toString()
        map["cash_fee"] = cashFee.toString()
        map["cash_add_fee"] = cashAddFee.toString()
        map["cash_add_wording"] = cashAddWording
        map["cash_reduced_fee"] = cashReducedFee.toString()
        map["cash_reduced_wording"] = cashReducedWording
        map["allow_fee_change"] = allowFeeChange.toString()
        map["spbill_create_ip"] = spbillCreateIp
        map["notify_url"] = notifyUrl
        map["limit_pay"] = limitPay
        map["body"] = body
        map["return_url"] = returnUrl
        map["pay_type"] = payType.toString()
        map["city_id"] = cityId
        map["consume_type"] = consumeType.toString()
        map["insurance_fee"] = insuranceFee.toString()
        map["user_card_type"] = userCardType.toString()
        map["user_card_no"] = userCardNo
        map["user_name"] = userName
        map["is_dept"] = isDept
        map["serial_no"] = serialNo
        map["org_no"] = orgNo
        map["gmt_out_create"] = gmtOutCreate
        map["request_content"] = requestContent
        map["bill_no"] = billNo
        map["extends"] = extends
        map["attach"] = attach
        map["channel_no"] = channelNo
    }

    override fun checkAndSign(config: WxPayConfig) {
        this.requestContent =
            """{"payAuthNo": "$payAuthNo", "payOrdId": "$payOrderId", "setlLatlnt": "$longitude,$latitude"}"""

        super.checkAndSign(config)
    }

}
