package com.github.binarywang.wxpay.bean.result

import com.thoughtworks.xstream.annotations.XStreamAlias
import me.chanjar.weixin.common.annotation.Required
import org.w3c.dom.Document

@XStreamAlias("xml")
class WxInsurancePayOrderQueryResult : BaseWxPayResult(), java.io.Serializable {

    companion object {
        private const val serialVersionUID: Long = -5915053912000605435L
    }

    /**
     * 交易状态
     * SUCCESS—支付成功
     * REFUND—转入退款
     * PAYING—支付中
     * SYS_REFUNDED-支付失败
     * SYS_REFUNDING-支付失败，异常处理中
     * NOTPAY—未支付
     * CLOSED—已关闭
     * INITIAL—未绑卡
     */
    @XStreamAlias("med_trade_state")
    var medTradeState: String = ""

    /**
     * 现金支付状态
     * SUCCESS—支付成功
     * REFUND—转入退款
     * NOTPAY—未支付
     * CLOSED—已关闭
     */
    @XStreamAlias("cash_trade_status")
    var cashTradeStatus: String = ""

    /**
     * 现金支付状态描述
     */
    val cashTradeStatusDescription: String
        get() = when (cashTradeStatus) {
            "SUCCESS" -> "支付成功"
            "REFUND" -> "转入退款"
            "NOTPAY" -> "未支付"
            "CLOSED" -> "已关闭"
            else -> cashTradeStatus.ifBlank { "未支付" }
        }

    /**
     * 医保支付状态
     * PAYED—已支付
     * REFUND—转入退款
     * NOTPAY—未支付
     * CLOSED—已关闭
     */
    @XStreamAlias("insurance_trade_status")
    var insuranceTradeStatus: String = ""

    /**
     * 对当前查询订单状态的描述和下一步操作的指引
     */
    @XStreamAlias("trade_status_desc")
    var tradeStatusDesc: String = ""

    /**
     * 订单支付时间，格式为yyyyMMddHHmmss
     */
    @XStreamAlias("time_end")
    var timeEnd: String = ""

    /**
     * 订单类型
     * RegPay=挂号支付
     * MedPay=药费支付
     * DiagPay=诊间支付
     * InHospPay=住院费支付
     * PharmacyPay=药店支付
     * InsurancePay=保险费支付
     * IntRegPay=互联网医院挂号支付
     * IntReDiagPay=互联网医院复诊支付
     * IntPscPay=互联网医院处方支付
     * CovidExamPay=新冠检测费用
     */
    @XStreamAlias("order_type")
    var orderType: String = ""

    /**
     * 支付类型
     * 1=现金
     * 2=医保
     * 3=现金+医保
     */
    @XStreamAlias("pay_type")
    var payType: Int = 0

    /**
     * 用户标识
     */
    @XStreamAlias("openid")
    var openid: String = ""

    /**
     * 用户标识
     */
    @XStreamAlias("sub_openid")
    var subOpenid: String = ""

    /**
     * 总共需要支付现金金额，对应医保移动支付中心支付下单接口的出参 feeSumamt
     */
    @XStreamAlias("total_fee")
    var totalFee: Int = 0

    /**
     * 现金需要支付的金额
     * 单位为分>=0，线上预结算模式金额填0，线下预结算模式填实际自费金额
     * 对应医保移动支付中心支付下单接口的出参 ownPayAmt
     */
    @XStreamAlias("cash_fee")
    var cashFee: Int = 0

    /**
     * 医保支付金额，单位为分>=0
     * 医保基金支付+个人账户支出，对应医保移动支付中心支付下单接口的出参 fundPay + psnAcctPay
     */
    @XStreamAlias("insurance_fee")
    var insuranceFee: Int? = null

    /**
     * 医保个账部分
     */
    @XStreamAlias("insurance_self_fee")
    var insuranceSelfFee: Int? = null

    /**
     * 医保统筹部分
     */
    @XStreamAlias("insurance_fund_fee")
    var insuranceFundFee: Int? = null

    /**
     * 医保其他部分
     */
    @XStreamAlias("insurance_other_fee")
    var insuranceOtherFee: Int? = null

    /**
     * 是否允许预结算费用发生变化
     */
    @XStreamAlias("allow_fee_change")
    var allowFeeChange: Int = 0

    /**
     * 用户端ip
     */
    @XStreamAlias("spbill_create_ip")
    var spbillCreateIp: String = ""

    /**
     * 回调url
     */
    @XStreamAlias("notify_url")
    var notifyUrl: String = ""

    /**
     * 第三方服务商订单号
     */
    @XStreamAlias("hosp_out_trade_no")
    var hospOutTradeNo: String = ""

    /**
     * 医院HIS系统订单号
     */
    @XStreamAlias("serial_no")
    var serialNo: String = ""

    /**
     * 医疗机构编码（医保局分配给机构）
     */
    @XStreamAlias("org_no")
    var orgNo: String = ""

    /**
     * 医院下单时间，格式为yyyyMMddHHmmss
     */
    @Required
    @XStreamAlias("gmt_out_create")
    var gmtOutCreate: String = ""

    /**
     * 参考医保结构体（医疗机构透传医保）
     */
    @XStreamAlias("request_content")
    var requestContent: String = ""

    /**
     * 业务单据号
     */
    @XStreamAlias("bill_no")
    var billNo: String = ""

    /**
     * no_credit--指定不能使用信用卡支付
     */
    @XStreamAlias("limit_pay")
    var limitPay: String = ""

    /**
     * 医院名称
     */
    @XStreamAlias("hospital_name")
    var hospitalName: String = ""

    /**
     * 支付后回跳的页面，不论成功或者失败均会回跳
     */
    @XStreamAlias("return_url")
    var returnUrl: String = ""

    /**
     * 支付完成后医保局返回内容串
     */
    @XStreamAlias("response_content")
    var responseContent: String = ""

    /**
     * 商品描述
     */
    @XStreamAlias("body")
    var body: String = ""

    /**
     * 扩展字段
     */
    @XStreamAlias("extends")
    var extends: String = ""

    /**
     * 微信生成的医疗订单号，类似于旧接口中的transaction_id
     */
    @XStreamAlias("med_trans_id")
    var medTransId: String = ""

    /**
     * 医保子单号
     */
    @XStreamAlias("insurance_order_id")
    var insuranceOrderId: String = ""

    /**
     * 微信支付子单号，可用于在微信支付商户平台查询对应的微信支付单
     */
    @XStreamAlias("cash_order_id")
    var cashOrderId: String = ""

    /**
     * 附加数据
     */
    @XStreamAlias("attach")
    var attach: String = ""

    override fun loadXml(d: Document) {
        medTradeState = readXmlString(d, "med_trade_state") ?: ""
        cashTradeStatus = readXmlString(d, "cash_trade_status") ?: ""
        insuranceTradeStatus = readXmlString(d, "insurance_trade_status") ?: ""
        tradeStatusDesc = readXmlString(d, "trade_status_desc") ?: ""
        timeEnd = readXmlString(d, "time_end") ?: ""
        orderType = readXmlString(d, "order_type") ?: ""
        payType = readXmlInteger(d, "pay_type") ?: 0
        openid = readXmlString(d, "openid") ?: ""
        subOpenid = readXmlString(d, "sub_openid") ?: ""
        totalFee = readXmlInteger(d, "total_fee") ?: 0
        cashFee = readXmlInteger(d, "cash_fee") ?: 0
        allowFeeChange = readXmlInteger(d, "allow_fee_change") ?: 0
        spbillCreateIp = readXmlString(d, "spbill_create_ip") ?: ""
        notifyUrl = readXmlString(d, "notify_url") ?: ""
        insuranceFee = readXmlInteger(d, "insurance_fee") ?: 0
        insuranceSelfFee = readXmlInteger(d, "insurance_self_fee") ?: 0
        insuranceFundFee = readXmlInteger(d, "insurance_fund_fee") ?: 0
        insuranceOtherFee = readXmlInteger(d, "insurance_other_fee") ?: 0
        hospOutTradeNo = readXmlString(d, "hosp_out_trade_no") ?: ""
        serialNo = readXmlString(d, "serial_no") ?: ""
        orgNo = readXmlString(d, "org_no") ?: ""
        gmtOutCreate = readXmlString(d, "gmt_out_create") ?: ""
        requestContent = readXmlString(d, "request_content") ?: ""
        billNo = readXmlString(d, "bill_no") ?: ""
        limitPay = readXmlString(d, "limit_pay") ?: ""
        hospitalName = readXmlString(d, "hospital_name") ?: ""
        returnUrl = readXmlString(d, "return_url") ?: ""
        responseContent = readXmlString(d, "response_content") ?: ""
        body = readXmlString(d, "body") ?: ""
        extends = readXmlString(d, "extends") ?: ""
        medTransId = readXmlString(d, "med_trans_id") ?: ""
        insuranceOrderId = readXmlString(d, "insurance_order_id") ?: ""
        cashOrderId = readXmlString(d, "cash_order_id") ?: ""
        attach = readXmlString(d, "attach") ?: ""
    }
}
