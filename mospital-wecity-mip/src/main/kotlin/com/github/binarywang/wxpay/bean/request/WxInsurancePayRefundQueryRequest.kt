package com.github.binarywang.wxpay.bean.request

import com.github.binarywang.wxpay.exception.WxPayException
import com.thoughtworks.xstream.annotations.XStreamAlias

@XStreamAlias("xml")
class WxInsurancePayRefundQueryRequest : BaseWxPayRequest() {

    companion object {
        private const val serialVersionUID: Long = 4572193255752973310L
    }

    /**
     * 微信生成的医疗订单号
     */
    @XStreamAlias("med_trans_id")
    var medTransId: String = ""

    /**
     * 第三方服务商订单号
     */
    @XStreamAlias("hosp_out_trade_no")
    var hospOutTradeNo: String = ""

    /**
     * 微信生成的退款医疗单号，四选一，建议使用med_refund_id
     */
    @XStreamAlias("med_refund_id")
    var medRefundId: String = ""

    /**
     * 医院退款订单号
     */
    @XStreamAlias("hosp_out_refund_no")
    var hospOutRefundNo: String = ""

    private fun isAllBlank(values: Array<String>): Boolean {
        return values.all { it.isBlank() }
    }

    private fun isAllNotBlank(values: Array<String>): Boolean {
        return values.all { it.isNotBlank() }
    }

    override fun checkConstraints() {
        val values = arrayOf(medTransId, hospOutTradeNo, medRefundId, hospOutRefundNo)
        if (isAllBlank(values) || isAllNotBlank(values)) {
            throw WxPayException("med_trans_id、hosp_out_trade_no、med_refund_id 和 hosp_out_refund_no 不能同时存在或同时为空，必须四选一")
        }
    }

    override fun storeMap(map: MutableMap<String, String>) {
        map["med_trans_id"] = medTransId
        map["hosp_out_trade_no"] = hospOutTradeNo
        map["med_refund_id"] = medRefundId
        map["hosp_out_refund_no"] = hospOutRefundNo
    }

}