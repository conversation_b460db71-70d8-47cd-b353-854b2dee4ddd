package com.github.binarywang.wxpay.bean.result

import com.thoughtworks.xstream.annotations.XStreamAlias
import org.w3c.dom.Document

@XStreamAlias("xml")
class WxInsurancePayDownloadBillResult : BaseWxPayResult(), java.io.Serializable {

    companion object {
        private const val serialVersionUID: Long = -1802231290906578488L
    }

    /**
     * 账单地址
     */
    @XStreamAlias("download_url")
    var downloadUrl: String = ""

    /**
     * 校验码值
     */
    @XStreamAlias("checksum_value")
    var checksumValue: String = ""

    /**
     *  校验算法
     */
    @XStreamAlias("checksum_type")
    var checksumType: String = ""

    override fun loadXml(d: Document) {
        downloadUrl = readXmlString(d, "download_url") ?: ""
        checksumValue = readXmlString(d, "checksum_value") ?: ""
        checksumType = readXmlString(d, "checksum_type") ?: ""
    }

}