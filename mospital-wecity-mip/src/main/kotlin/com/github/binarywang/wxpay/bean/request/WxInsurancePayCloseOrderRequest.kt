package com.github.binarywang.wxpay.bean.request

import com.github.binarywang.wxpay.exception.WxPayException
import com.thoughtworks.xstream.annotations.XStreamAlias

@XStreamAlias("xml")
class WxInsurancePayCloseOrderRequest() : BaseWxPayRequest() {

    companion object {
        private const val serialVersionUID: Long = -4400422674652254850L
    }

    /**
     * 微信支付生成的医疗订单号
     */
    @XStreamAlias("med_trans_id")
    var medTransId: String = ""

    /**
     * 第三方服务商订单号
     */
    @XStreamAlias("hosp_out_trade_no")
    var hospOutTradeNo: String = ""

    private fun isAllBlank(values: Array<String>): Boolean {
        return values.all { it.isBlank() }
    }

    private fun isAllNotBlank(values: Array<String>): Boolean {
        return values.all { it.isNotBlank() }
    }

    override fun checkConstraints() {
        val values = arrayOf(medTransId, hospOutTradeNo)
        if (isAllBlank(values) || isAllNotBlank(values)) {
            throw WxPayException("med_trans_id 和 hosp_out_trade_no 不能同时存在或同时为空，必须二选一")
        }
    }

    override fun storeMap(map: MutableMap<String, String>) {
        map["med_trans_id"] = medTransId
        map["hosp_out_trade_no"] = hospOutTradeNo
    }

}
