package com.github.binarywang.wxpay.bean.notify

import com.github.binarywang.wxpay.bean.result.BaseWxPayResult
import com.github.binarywang.wxpay.constant.WxPayConstants
import com.github.binarywang.wxpay.exception.WxPayException
import com.github.binarywang.wxpay.service.WxPayService
import com.thoughtworks.xstream.annotations.XStreamAlias
import org.w3c.dom.Document

@XStreamAlias("xml")
class WxInsuranceOrderNotifyResult : BaseWxPayResult(), java.io.Serializable {

    companion object {
        private const val serialVersionUID: Long = -8680901883052558429L

        fun fromXML(xmlString: String): WxInsuranceOrderNotifyResult {
            return fromXML(xmlString, WxInsuranceOrderNotifyResult::class.java)
        }
    }

    /**
     * 用户标识
     */
    @XStreamAlias("openid")
    var openid: String = ""

    /**
     * 用户标识
     */
    @XStreamAlias("sub_openid")
    var subOpenid: String = ""

    /**
     * 微信生成的医疗订单号，类似于旧接口中的transaction_id
     */
    @XStreamAlias("med_trans_id")
    var medTransId: String = ""

    /**
     * 第三方服务商订单号
     */
    @XStreamAlias("hosp_out_trade_no")
    var hospOutTradeNo: String = ""

    /**
     * 订单支付时间，格式为yyyyMMddHHmmss
     */
    @XStreamAlias("time_end")
    var timeEnd: String = ""

    /**
     * 支付类型
     * 1=现金
     * 2=医保
     * 3=现金+医保
     */
    @XStreamAlias("pay_type")
    var payType: Int = 0

    /**
     * 总共需要支付现金金额，对应医保移动支付中心支付下单接口的出参 feeSumamt
     */
    @XStreamAlias("total_fee")
    var totalFee: Int = 0

    /**
     * 现金需要支付的金额
     * 单位为分>=0，线上预结算模式金额填0，线下预结算模式填实际自费金额
     * 对应医保移动支付中心支付下单接口的出参 ownPayAmt
     */
    @XStreamAlias("cash_fee")
    var cashFee: Int = 0

    /**
     * 医保支付金额，单位为分>=0
     * 医保基金支付+个人账户支出，对应医保移动支付中心支付下单接口的出参 fundPay + psnAcctPay
     */
    @XStreamAlias("insurance_fee")
    var insuranceFee: Int? = null

    /**
     * 医保个账部分
     */
    @XStreamAlias("insurance_self_fee")
    var insuranceSelfFee: Int? = null

    /**
     * 医保统筹部分
     */
    @XStreamAlias("insurance_fund_fee")
    var insuranceFundFee: Int? = null

    /**
     * 医保其他部分
     */
    @XStreamAlias("insurance_other_fee")
    var insuranceOtherFee: Int? = null

    /**
     * 支付完成后医保局返回内容串
     */
    @XStreamAlias("response_content")
    var responseContent: String = ""

    /**
     * 业务单据号
     */
    @XStreamAlias("bill_no")
    var billNo: String = ""

    /**
     * 医院HIS系统订单号
     */
    @XStreamAlias("serial_no")
    var serialNo: String = ""

    /**
     * 医保子单号
     */
    @XStreamAlias("insurance_order_id")
    var insuranceOrderId: String = ""

    /**
     * 微信支付子单号，可用于在微信支付商户平台查询对应的微信支付单
     */
    @XStreamAlias("cash_order_id")
    var cashOrderId: String = ""

    /**
     * 附加数据
     */
    @XStreamAlias("attach")
    var attach: String = ""

    override fun checkResult(wxPayService: WxPayService, signType: String, checkSuccess: Boolean) {
        //防止伪造成功通知
        if (WxPayConstants.ResultCode.SUCCESS == getReturnCode() && sign == null) {
            throw WxPayException("伪造的通知！")
        }

        super.checkResult(wxPayService, signType, checkSuccess)
    }

    override fun loadXml(d: Document?) {
        timeEnd = readXmlString(d, "time_end")
        payType = readXmlInteger(d, "pay_type")
        openid = readXmlString(d, "openid")
        subOpenid = readXmlString(d, "sub_openid").orEmpty()
        totalFee = readXmlInteger(d, "total_fee")
        cashFee = readXmlInteger(d, "cash_fee")
        insuranceFee = readXmlInteger(d, "insurance_fee")
        insuranceSelfFee = readXmlInteger(d, "insurance_self_fee")
        insuranceFundFee = readXmlInteger(d, "insurance_fund_fee")
        insuranceOtherFee = readXmlInteger(d, "insurance_other_fee")
        hospOutTradeNo = readXmlString(d, "hosp_out_trade_no")
        serialNo = readXmlString(d, "serial_no")
        billNo = readXmlString(d, "bill_no").orEmpty()
        responseContent = readXmlString(d, "response_content")
        medTransId = readXmlString(d, "med_trans_id")
        insuranceOrderId = readXmlString(d, "insurance_order_id")
        cashOrderId = readXmlString(d, "cash_order_id").orEmpty()
        attach = readXmlString(d, "attach").orEmpty()
    }

}
