package com.github.binarywang.wxpay.bean.result

import com.thoughtworks.xstream.annotations.XStreamAlias
import org.w3c.dom.Document

@XStreamAlias("xml")
class WxInsurancePayUnifiedOrderResult : BaseWxPayResult(), java.io.Serializable {

    companion object {
        private const val serialVersionUID: Long = -5246735024905752597L
    }

    /**
     * 诊疗单id，微信生成的医疗订单id，用于后续调用接口使用
     */
    @XStreamAlias("med_trans_id")
    var medTransId: String = ""

    /**
     * 支付链接
     * 下单后跳转到此url，用户完成支付
     */
    @XStreamAlias("pay_url")
    var payUrl: String = ""

    /**
     * 支付小程序
     * 当使用医院小程序进行下单时，会返回此参数，医院小程序跳转至该支付小程序进行支付
     */
    @XStreamAlias("pay_appid")
    var payAppId: String = ""

    override fun loadXml(d: Document) {
        medTransId = readXmlString(d, "med_trans_id") ?: ""
        payUrl = readXmlString(d, "pay_url") ?: ""
        payAppId = readXmlString(d, "pay_appid") ?: ""
    }

}