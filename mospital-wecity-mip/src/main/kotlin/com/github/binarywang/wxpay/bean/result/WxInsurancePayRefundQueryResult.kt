package com.github.binarywang.wxpay.bean.result

import com.thoughtworks.xstream.annotations.XStreamAlias
import org.w3c.dom.Document

@XStreamAlias("xml")
class WxInsurancePayRefundQueryResult : BaseWxPayResult(), java.io.Serializable {

    companion object {
        private const val serialVersionUID: Long = -2981589149267512157L
    }

    /**
     * 微信生成的医疗订单号
     */
    @XStreamAlias("med_trans_id")
    var medTransId: String = ""

    /**
     * 第三方服务商订单号
     */
    @XStreamAlias("hosp_out_trade_no")
    var hospOutTradeNo: String = ""

    /**
     * 微信生成的退款医疗单号
     */
    @XStreamAlias("med_refund_id")
    var medRefundId: String = ""

    /**
     * 医院退款订单号
     */
    @XStreamAlias("hosp_out_refund_no")
    var hospOutRefundNo: String = ""

    /**
     * 医保退款金额（目前只能全额退）
     */
    @XStreamAlias("insurance_refund_fee")
    var insuranceRefundFee: Int? = null

    /**
     * 现金退款金额
     */
    @XStreamAlias("cash_refund_fee")
    var cashRefundFee: Int? = null

    /**
     * 现金退款状态
     * SUCCESS—退款成功
     * REFUNDING—退款处理中
     * CHANGE—转入代发，退款到银行发现用户的卡作废或者冻结了，导致原路退款银行卡失败，资金回流到商户的现金帐号，需要商户人工干预，通过线下或者财付通转账的方式进行退款。
     */
    @XStreamAlias("cash_refund_stauts")
    var cashRefundStauts: String = ""

    /**
     * 医保退款状态
     * SUCCESS—退款成功
     * FAIL—退款失败
     * REFUNDING—退款处理中
     */
    @XStreamAlias("insurance_refund_status")
    var insuranceRefundStauts: String = ""

    /**
     * 退款单总状态
     * SUCCESS—退款成功
     * REFUNDING—退款处理中
     */
    @XStreamAlias("med_refund_state")
    var medRefundState: String = ""

    /**
     * 退款成功时间
     */
    @XStreamAlias("refund_end_time")
    var refundEndTime: String = ""

    /**
     * 退款完成后医保局返回内容串
     */
    @XStreamAlias("response_content")
    var responseContent: String = ""

    /**
     * 微信医保支付生成的微信支付退款订单号
     */
    @XStreamAlias("cash_refund_id")
    var cashRefundId: String = ""

    /**
     * 微信医保支付生成的医保退款单号
     */
    @XStreamAlias("insurance_refund_id")
    var insuranceRefundId: String = ""

    override fun loadXml(d: Document) {
        // do nothing
    }


}