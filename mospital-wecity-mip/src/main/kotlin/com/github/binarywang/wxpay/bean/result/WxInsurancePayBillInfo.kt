package com.github.binarywang.wxpay.bean.result

import org.dromara.hutool.poi.csv.CsvReadConfig
import org.dromara.hutool.poi.csv.CsvUtil
import java.math.BigDecimal

/**
 * 医保对账单
 * @property tradeTime String 交易时间
 * @property appId String 公众账号ID
 * @property mchId String 商户号
 * @property subMchId String 子商户号
 * @property deviceInfo String 设备号
 * @property transactionId String 微信订单号
 * @property outTradeNo String 商户订单号
 * @property openId String 用户标识
 * @property tradeType String 交易类型
 * @property tradeState String 交易状态
 * @property bankType String 付款银行
 * @property currencyCode String 货币种类
 * @property totalFee String 总金额
 * @property couponFee String 代金券或立减优惠金额
 * @property refundId String 微信退款单号
 * @property outRefundNo String 商户退款单号
 * @property settlementRefundFee String 退款金额
 * @property couponRefundFee String 代金券或立减优惠退款金额
 * @property refundChannel String 退款类型
 * @property refundState String 退款状态
 * @property body String 商品名称
 * @property attach String 商户数据包
 * @property poundage String 手续费
 * @property poundageRate String 费率
 * Todo 医保支付流水 I23022087717928183
 * @property hospOutTradeNo String 医院订单号
 * Todo 医保业务流水号 TSN650100202302200951330101351
 * Todo 医保业务单据号
 * Todo 医疗机构退款流水号
 * Todo 医保撤销流水号
 * Todo 医保撤销单据号
 * @property orgNo String 医疗机构编码
 * Todo 医保外部交易支付结算成功时间
 * @property insuranceFundFee String 医保支付资金（医保基金支付金额）
 * @property insuranceSelfFee String 医保自费资金
 * @property insuranceFee String 医保总交易金额
 * Todo 撤销金额
 * Todo 医保外部撤销成功时间
 * @property hospitalName String 医院名称
 * @property orderType String 订单支付类型
 * @property allowFeeChange String 是否允许预结算费用发生变化
 * @property payType 订单支付方式
 * Todo 订单号 ZJ202302200951467188
 * Todo 退款订单号
 * Todo 订单下单时间
 * Todo 订单状态
 * Todo 订单自费金额
 * Todo 订单医保金额
 * Todo 订单总金额
 * Todo 微信医保支付总单号
 * @property medTransId String 微信医保支付总单号
 * Todo 微信医保支付退款总单号
 * Todo 是否已进行医保对账
 * Todo 医保个账金额
 * Todo 医保统筹金额
 * Todo 医保其他金额
 * Todo 系统参考号
 * Todo 医保个账金额退款金额
 * Todo 医保统筹退款金额
 */
@Suppress("unused")
data class WxInsurancePayBillInfo(
    val tradeTime: String,
    val appId: String,
    val mchId: String,
    val subMchId: String,
    val deviceInfo: String,
    val transactionId: String,
    val outTradeNo: String,
    val openId: String,
    val tradeType: String,
    val tradeState: String,
    val bankType: String,
    val currencyCode: String,
    val totalFee: String,
    val couponFee: String,
    val refundId: String,
    val outRefundNo: String,
    val settlementRefundFee: String,
    val couponRefundFee: String,
    val refundChannel: String,
    val refundState: String,
    val body: String,
    val attach: String,
    val poundage: String,
    val poundageRate: String,
    val hospOutTradeNo: String,
    val orgNo: String,
    val insuranceFundFee: String,
    val insuranceSelfFee: String,
    val insuranceFee: String,
    val hospitalName: String,
    val orderType: String,
    val allowFeeChange: String,
    val payType: String,
    val medTransId: String,
) {

    fun isPay(): Boolean = refundId.isBlank()
    fun isRefund(): Boolean = !isPay()
    fun getPayAmount(): BigDecimal = try {
        if (isPay()) totalFee.toBigDecimal() else BigDecimal.ZERO
    } catch (e: Exception) {
        BigDecimal.ZERO
    }
    fun getRefundAmount(): BigDecimal = try {
        if (isRefund()) settlementRefundFee.toBigDecimal() else BigDecimal.ZERO
    } catch (e: Exception) {
        BigDecimal.ZERO
    }

    companion object {
        private const val serialVersionUID: Long = -1802231290906578488L

        fun fromRawBillResultString(responseContent: String, billType: String): List<WxInsurancePayBillInfo> =
            when (billType) {
                "ALL" -> fromRawBillResultStringToAll(responseContent)
                "SUCCESS" -> fromRawBillResultStringToSuccess(responseContent)
                "REFUND" -> fromRawBillResultStringToRefund(responseContent)
                else -> emptyList<WxInsurancePayBillInfo>()
            }

        private fun fromRawBillResultStringToAll(responseContent: String): List<WxInsurancePayBillInfo> {
            val bills = mutableListOf<WxInsurancePayBillInfo>()
            CsvUtil.getReader(CsvReadConfig().setBeginLineNo(0L).setHeaderLineNo(0L)).readFromStr(responseContent) { row ->
                val bill = WxInsurancePayBillInfo(
                    tradeTime = row.getByName("交易时间").removePrefix("`"),
                    appId = row.getByName("公众账号ID").removePrefix("`"),
                    mchId = row.getByName("商户号").removePrefix("`"),
                    subMchId = row.getByName("子商户号").removePrefix("`"),
                    deviceInfo = row.getByName("设备号").removePrefix("`"),
                    transactionId = row.getByName("微信订单号").removePrefix("`"),
                    outTradeNo = row.getByName("商户订单号").removePrefix("`"),
                    openId = row.getByName("用户标识").removePrefix("`"),
                    tradeType = row.getByName("交易类型").removePrefix("`"),
                    tradeState = row.getByName("交易状态").removePrefix("`"),
                    bankType = row.getByName("付款银行").removePrefix("`"),
                    currencyCode = row.getByName("货币种类").removePrefix("`"),
                    totalFee = row.getByName("总金额").removePrefix("`"),
                    couponFee = row.getByName("代金券或立减优惠金额").removePrefix("`"),
                    refundId = row.getByName("微信退款单号").removePrefix("`"),
                    outRefundNo = row.getByName("商户退款单号").removePrefix("`"),
                    settlementRefundFee = row.getByName("退款金额").removePrefix("`"),
                    couponRefundFee = row.getByName("代金券或立减优惠退款金额").removePrefix("`"),
                    refundChannel = row.getByName("退款类型").removePrefix("`"),
                    refundState = row.getByName("退款状态").removePrefix("`"),
                    body = row.getByName("商品名称").removePrefix("`"),
                    attach = row.getByName("商户数据包").removePrefix("`"),
                    poundage = row.getByName("手续费").removePrefix("`"),
                    poundageRate = row.getByName("费率").removePrefix("`"),
                    hospOutTradeNo = row.getByName("医院订单号").removePrefix("`"),
                    orgNo = row.getByName("医疗机构编码").removePrefix("`"),
                    insuranceFundFee = row.getByName("医保支付资金").removePrefix("`"),
                    insuranceSelfFee = row.getByName("医保自费资金").removePrefix("`"),
                    insuranceFee = row.getByName("医保总交易金额").removePrefix("`"),
                    hospitalName = row.getByName("医院名称").removePrefix("`"),
                    orderType = row.getByName("订单支付类型").removePrefix("`"),
                    allowFeeChange = row.getByName("是否允许预结算费用发生变化").removePrefix("`"),
                    payType = row.getByName("订单支付方式").removePrefix("`"),
                    medTransId = row.getByName("微信医保支付总单号").removePrefix("`"),
                )
                bills.add(bill)
            }
            return bills
        }

        // Todo 从原始对账单字符串中解析出成功的交易记录
        private fun fromRawBillResultStringToSuccess(responseContent: String): List<WxInsurancePayBillInfo> {
            return emptyList()
        }

        // Todo 从原始对账单字符串中解析出退款的交易记录
        private fun fromRawBillResultStringToRefund(responseContent: String): List<WxInsurancePayBillInfo> {
            return emptyList()
        }
    }
}