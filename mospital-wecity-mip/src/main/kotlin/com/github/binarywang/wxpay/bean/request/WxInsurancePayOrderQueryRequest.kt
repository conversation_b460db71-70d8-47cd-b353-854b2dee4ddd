package com.github.binarywang.wxpay.bean.request

import com.github.binarywang.wxpay.exception.WxPayException
import com.thoughtworks.xstream.annotations.XStreamAlias

@XStreamAlias("xml")
class WxInsurancePayOrderQueryRequest() : BaseWxPayRequest() {

    companion object {
        private const val serialVersionUID: Long = -231844919780593895L
    }

    constructor(medTransId: String, hospOutTradeNo: String): this() {
        this.medTransId = medTransId
        this.hospOutTradeNo = hospOutTradeNo
    }

    /**
     * 微信生成的医疗订单号，与hosp_out_trade_no二选一，建议使用med_trans_id
     */
    @XStreamAlias("med_trans_id")
    var medTransId: String = ""

    /**
     * 第三方服务商订单号，与med_trans_id二选一，建议使用med_trans_id
     */
    @XStreamAlias("hosp_out_trade_no")
    var hospOutTradeNo: String = ""

    private fun isAllBlank(values: Array<String>): Boolean {
        return values.all { it.isBlank() }
    }

    private fun isAllNotBlank(values: Array<String>): Boolean {
        return values.all { it.isNotBlank() }
    }

    override fun checkConstraints() {
        val values = arrayOf(medTransId, hospOutTradeNo)
        if (isAllBlank(values) || isAllNotBlank(values)) {
            throw WxPayException("med_trans_id 和 hosp_out_trade_no 不能同时存在或同时为空，必须二选一")
        }
    }

    override fun storeMap(map: MutableMap<String, String>) {
        map["med_trans_id"] = medTransId
        map["hosp_out_trade_no"] = hospOutTradeNo
    }

}