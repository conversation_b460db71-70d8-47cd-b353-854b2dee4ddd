package com.github.binarywang.wxpay.bean.request

import com.github.binarywang.wxpay.config.WxPayConfig
import com.thoughtworks.xstream.annotations.XStreamAlias
import com.thoughtworks.xstream.annotations.XStreamOmitField
import me.chanjar.weixin.common.annotation.Required

@XStreamAlias("xml")
class WxInsurancePayRefundRequest() : BaseWxPayRequest() {

    companion object {
        private const val serialVersionUID: Long = -325175439761417119L
    }

    /**
     * 微信生成的医疗订单号，与hosp_out_trade_no二选一，建议使用med_trans_id
     */
    @XStreamAlias("med_trans_id")
    var medTransId: String = ""

    /**
     * 第三方服务商订单号，与med_trans_id二选一，建议使用med_trans_id
     */
    @XStreamAlias("hosp_out_trade_no")
    var hospOutTradeNo: String = ""

    /**
     * 医院退款订单号
     * 退款订单号在商户侧唯一，同一个hosp_out_trade_no只能使用唯一的hosp_out_refund_no，实现上可以考虑使用：前缀R+hosp_out_trade_no
     */
    @Required
    @XStreamAlias("hosp_out_refund_no")
    var hospOutRefundNo: String = ""

    /**
     * 撤销流水号
     * 医保退款必填，医保局无要求则由医院自定义
     */
    @XStreamAlias("cancel_serial_no")
    var cancelSerialNo: String = ""

    /**
     * 撤销单据号
     * 医保退款必填，医保局无要求则由医院自定义
     */
    @XStreamAlias("cancel_bill_no")
    var cancelBillNo: String = ""

    /**
     * 部分退款
     * 非必填，不填则整单退款，CASH_ONLY则只退现金部分。
     * 现金跟医保可以分开退款，医保部分调中台【6203】接口退款。
     */
    @XStreamAlias("part_refund_type")
    var partRefundType: String = ""

    /**
     * 现金部分退款
     * 不填默认全退，填写时则退指定金额，不能为0，仅当part_refund_type为空或CASH_ONLY时生效
     */
    @XStreamAlias("cash_refund_fee")
    var cashRefundFee: String = ""

    /**
     * 透传医保局
     */
    @Required
    @XStreamAlias("request_content")
    var requestContent: String = ""

    /**
     * 向医保上传费用明细时产生的支付订单号
     */
    @Required
    @XStreamOmitField
    var payOrderId: String = ""

    /**
     * 退款原因
     */
    @Required
    @XStreamOmitField
    var refReason: String = ""

    override fun checkConstraints() {

    }

    override fun checkAndSign(config: WxPayConfig) {
        this.requestContent =
            """{"payOrderId": "$payOrderId", "ref_reason": "$refReason"}"""

        super.checkAndSign(config)
    }

    override fun getIgnoredParamsForSign(): Array<String> {
        return arrayOf("payOrderId", "refReason")
    }

    override fun storeMap(map: MutableMap<String, String>) {
        map["med_trans_id"] = medTransId
        map["hosp_out_trade_no"] = hospOutTradeNo
        map["hosp_out_refund_no"] = hospOutRefundNo
        map["cancel_serial_no"] = cancelSerialNo
        map["cancel_bill_no"] = cancelBillNo
        map["part_refund_type"] = partRefundType
        map["cash_refund_fee"] = cashRefundFee
        map["request_content"] = requestContent
    }

}