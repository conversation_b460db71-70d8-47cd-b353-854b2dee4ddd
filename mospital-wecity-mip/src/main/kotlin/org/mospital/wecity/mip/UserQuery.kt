package org.mospital.wecity.mip

import com.fasterxml.jackson.annotation.JsonAlias

data class UserQueryRequest(val qrcode: String, val openid: String)

data class LongitudeLatitude(val longitude: String, val latitude: String) {
    override fun toString(): String {
        return "$longitude,$latitude"
    }
}

/**
 * 用户查询结果，参见：<a href="https://docs.qq.com/doc/DV3JYRG1xelhKTWdz">用户授权接入文档</a>
 * @param familyPayAuthNo 当使用医保亲情线上支付功能时，可以返回，与pay_auth_no为二选一，返回该参数时，不返回pay_auth_no
 */
data class UserQueryResult(
    @JsonAlias("code") val code: Int,
    @JsonAlias("message") val message: String,
    @JsonAlias("message_extra") val extraMessage: String?,
    @JsonAlias("user_name") val userName: String?,
    @JsonAlias("user_card_no") val userCardNo: String?,
    @JsonAlias("user_card_type") val userCardType: String?,
    @JsonAlias("city_id") val cityId: String?,
    @JsonAlias("pay_auth_no") val payAuthNo: String?,
    @JsonAlias("family_pay_auth_no") val familyPayAuthNo: String?,
    @JsonAlias("user_longitude_latitude") val longitudeLatitude: LongitudeLatitude?,
) {
    fun isOK() = code == 0

    /**
     * 是否亲情医保移动支付
     */
    fun isFamilyPay() = isOK() && !familyPayAuthNo.isNullOrEmpty()

    /**
     * 是否本人医保移动支付
     */
    fun isSelfPay() = isOK() && !payAuthNo.isNullOrEmpty()

}

