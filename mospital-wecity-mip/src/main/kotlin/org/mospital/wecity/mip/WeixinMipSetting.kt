package org.mospital.wecity.mip

import org.mospital.jackson.ConfigLoader

// Helper object to load the entire YAML configuration once
internal object WecityMipRootConfigLoader {
    val config: WecityMipConfig by lazy {
        ConfigLoader.loadConfig("wecity-mip.yaml", WecityMipConfig::class.java, WecityMipRootConfigLoader::class.java)
    }
}

class WeixinMipSetting { // Made constructor private as instances are managed via companion object

    companion object {
        val ma: WeixinMipSetting by lazy { WeixinMipSetting("maMip") }
        val mp: WeixinMipSetting by lazy { WeixinMipSetting("mpMip") }

        fun getInsance(clientType: ClientType): WeixinMipSetting {
            return when (clientType) {
                ClientType.MA -> ma
                ClientType.MP -> mp
                // else -> throw IllegalArgumentException("Unsupported clientType: $clientType")
            }
        }

        private const val PROD_SERVICE_URL = "https://med-biz.wecity.qq.com/"
        private const val TEST_SERVICE_URL = "https://med-biz-pre.wecity.qq.com/"
    }

    private val mipConfig: MipConfig // Stores the loaded MaMipConfig

    private constructor(clientType: String) { // Constructor is now private
        if (clientType.equals("maMip", ignoreCase = true)) {
            this.mipConfig = WecityMipRootConfigLoader.config.maMip
        } else if (clientType.equals("mpMip", ignoreCase = true)) {
            this.mipConfig = WecityMipRootConfigLoader.config.mpMip
        } else {
            throw IllegalArgumentException("Unsupported clientType for WeixinMipSetting: $clientType. Only 'maMip' or 'mpMip' is configured.")
        }
    }

    private val env: String
        get() = mipConfig.env.ifEmpty { "prod" }

    val partnerId: String
        get() = mipConfig.partnerId

    val partnerKey: String
        get() = mipConfig.partnerKey

    val channelNo: String
        get() = mipConfig.channelNo

    val orgCode: String
        get() = mipConfig.orgCode

    val cityId: String
        get() = mipConfig.cityId

    val mipKey: String
        get() = mipConfig.mipKey

    val orgAppId: String
        get() = mipConfig.orgAppId

    val orgChannelCode: String
        get() = mipConfig.orgChannelCode

    val appId: String // This is the specific appId under maMip, not to be confused with orgAppId
        get() = mipConfig.appId


    val url: String
        get() = when (env.lowercase()) { // Use lowercase for comparison
            "prod" -> PROD_SERVICE_URL
            "test" -> TEST_SERVICE_URL
            else -> PROD_SERVICE_URL // Default to prod if env is something else or empty
        }

    fun isProd(): Boolean {
        return env.equals("prod", ignoreCase = true)
    }

}