package org.mospital.wecity.mip

/**
 * WecityMipConfig 配置类
 * 对应 wecity-mip.yaml 配置文件
 */
data class WecityMipConfig(
    val maMip: MipConfig = MipConfig(),
    val mpMip: MipConfig = MipConfig()
)

data class MipConfig(
    val env: String = "",
    val partnerId: String = "",
    val partnerKey: String = "",
    val channelNo: String = "",
    val cityId: String = "",
    val orgCode: String = "",
    val mipKey: String = "",
    val orgAppId: String = "",
    val orgChannelCode: String = "",
    val appId: String = ""
)
