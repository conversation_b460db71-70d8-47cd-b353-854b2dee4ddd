package org.mospital.wecity.mip

import org.dromara.hutool.crypto.SecureUtil
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response
import org.mospital.common.IdUtil

class WecityMipInterceptor(val weixinMipSetting: WeixinMipSetting): Interceptor {

    private val hmacTL = ThreadLocal.withInitial { SecureUtil.hmacSha256(weixinMipSetting.partnerKey) }

    override fun intercept(chain: Interceptor.Chain): Response {
        val timestamp: Long = System.currentTimeMillis()
        val requestId: String = IdUtil.simpleUUID()
        val signature: String = hmacTL.get().digestHex("${weixinMipSetting.partnerId}$timestamp")
        val newRequest: Request = chain.request().newBuilder()
            .header("god-portal-timestamp", timestamp.toString())
            .addHeader("god-portal-request-id", requestId)
            .addHeader("god-portal-signature", signature)
            .build()
        return chain.proceed(newRequest)
    }

}
