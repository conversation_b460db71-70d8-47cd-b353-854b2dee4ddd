package org.mospital.wecity.mip

import com.github.binarywang.wxpay.bean.result.WxInsurancePayBillInfo
import org.junit.jupiter.api.Test
import kotlin.test.assertTrue

class WxInsurancePayBillInfoTest {

    @Test
    fun testParse() {
        val rawBillString = """交易时间,公众账号ID,商户号,子商户号,设备号,微信订单号,商户订单号,用户标识,交易类型,交易状态,付款银行,货币种类,总金额,代金券或立减优惠金额,微信退款单号,商户退款单号,退款金额,代金券或立减优惠退款金额,退款类型,退款状态,商品名称,商户数据包,手续费,费率,医保支付流水,医院订单号,医保业务流水号,医保业务单据号,医疗机构退款流水号,医保撤销流水号,医保撤销单据号,医疗机构编码,医保外部交易支付结算成功时间,医保支付资金,医保自费资金,医保总交易金额,撤销金额,医保外部撤销成功时间,医院名称,订单支付类型,是否允许预结算费用发生变化,订单支付方式,订单号,退款订单号,订单下单时间,订单状态,订单自费金额,订单医保金额,订单总金额,微信医保支付总单号,微信医保支付退款总单号,是否已进行医保对账,医保个账金额,医保统筹金额,医保其他金额,系统参考号,医保个账金额退款金额,医保统筹退款金额
`2023-02-20 13:18:36,`wxd3a4805a3be9b62e,`**********,`,`,`4200001727202302200814211258,`C23022087717987257,`oQ4yW5WrjdahD7jzoR2bYT2uAbA8,`JSAPI,`SUCCESS,`OTHERS,`CNY,`91.73,`0.00,`,`,`,`,`,`,`伊犁哈萨克自治州友谊医院-诊间缴费,`,`0.00000,`0.00%,`I23022087717987257,`ZJ202302201318180066,`TSN650100202302201318090102835,`,`,`,`,`H65400200247,`2023-02-20 13:18:36,`532.27,`91.73,`624.00,`,`,`伊犁哈萨克自治州友谊医院,`DiagPay,`0,`3,`ZJ202302201318180066,`,`2023-02-20 13:18:19,`SUCCESS,`91.73,`532.27,`624.00,`M23022087717987257,`,`,`211.40,`320.87,`0.00,`,`,`
"""
        val bills = WxInsurancePayBillInfo.fromRawBillResultString(rawBillString, "ALL")
        assertTrue(bills.size == 1)

        val bill = bills[0]
        assertTrue(bill.tradeTime == "2023-02-20 13:18:36")
        assertTrue(bill.appId == "wxd3a4805a3be9b62e")
        assertTrue(bill.mchId == "**********")
        assertTrue(bill.subMchId == "")
        assertTrue(bill.deviceInfo == "")
        assertTrue(bill.transactionId == "4200001727202302200814211258")
        assertTrue(bill.outTradeNo == "C23022087717987257")
        assertTrue(bill.openId == "oQ4yW5WrjdahD7jzoR2bYT2uAbA8")
        assertTrue(bill.tradeType == "JSAPI")
        assertTrue(bill.tradeState == "SUCCESS")
        assertTrue(bill.bankType == "OTHERS")
        assertTrue(bill.currencyCode == "CNY")
        assertTrue(bill.totalFee == "91.73")
        assertTrue(bill.couponFee == "0.00")
        assertTrue(bill.refundId == "")
        assertTrue(bill.outRefundNo == "")
        assertTrue(bill.settlementRefundFee == "")
        assertTrue(bill.couponRefundFee == "")
        assertTrue(bill.refundChannel == "")
        assertTrue(bill.refundState == "")
        assertTrue(bill.body == "伊犁哈萨克自治州友谊医院-诊间缴费")
        assertTrue(bill.attach == "")
        assertTrue(bill.poundage == "0.00000")
        assertTrue(bill.poundageRate == "0.00%")
        assertTrue(bill.hospOutTradeNo == "ZJ202302201318180066")
        assertTrue(bill.orgNo == "H65400200247")
        assertTrue(bill.insuranceFundFee == "532.27")
        assertTrue(bill.insuranceSelfFee == "91.73")
        assertTrue(bill.insuranceFee == "624.00")
        assertTrue(bill.hospitalName == "伊犁哈萨克自治州友谊医院")
        assertTrue(bill.orderType == "DiagPay")
        assertTrue(bill.allowFeeChange == "0")
        assertTrue(bill.payType == "3")
        assertTrue(bill.medTransId == "M23022087717987257")
    }

}