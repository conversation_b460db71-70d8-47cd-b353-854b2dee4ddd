package org.mospital.wecity.mip

import com.github.binarywang.wxpay.bean.result.BaseWxPayResult
import com.github.binarywang.wxpay.bean.result.WxInsurancePayUnifiedOrderResult
import com.github.binarywang.wxpay.util.XmlConfig
import org.junit.jupiter.api.Test

class BaseWxPayResultTest {

    @Test
    fun testFromXML() {
        val responseContent = """
            <xml>
            <return_code><![CDATA[SUCCESS]]></return_code>
            <result_code><![CDATA[SUCCESS]]></result_code>
            <err_code><![CDATA[0]]></err_code>
            <appid><![CDATA[wxebe51b3e144b2173]]></appid>
            <sub_appid><![CDATA[]]></sub_appid>
            <mch_id><![CDATA[1518861501]]></mch_id>
            <sub_mch_id><![CDATA[]]></sub_mch_id>
            <med_trans_id><![CDATA[M24042803291385295]]></med_trans_id>
            <pay_url><![CDATA[https://mp.weixin.qq.com/insurance/pay/jump?pay_key=rY175mzGx0Clj7z_3WY8pHInHE2juoc5nTF2J3kUyq4blRfoEjxKOvudlS8c1bRXbjEvoINc6KKFNvkqyx9XHQ..#wechat_redirect]]></pay_url>
            <nonce_str><![CDATA[1714292863756]]></nonce_str>
            <sign><![CDATA[CD1E7E3A04E47E237AA8EC707CC1EA2A]]></sign>
            </xml>
        """.trimIndent()
        XmlConfig.fastMode = true
        val result = BaseWxPayResult.fromXML(responseContent, WxInsurancePayUnifiedOrderResult::class.java)
        println(result)
    }
}