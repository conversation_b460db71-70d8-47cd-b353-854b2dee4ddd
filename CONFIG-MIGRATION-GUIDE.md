# 配置迁移指南

从 Hutool Setting 迁移到 ConfigLoader + YAML 配置格式

## 📋 概述

本指南将帮助您将项目中的 Hutool Setting 配置文件（`.setting` 格式）迁移到使用 `ConfigLoader` 和 YAML 格式的配置系统。

## 🎯 迁移目标

- ✅ 统一配置文件格式（YAML）
- ✅ 类型安全的配置加载
- ✅ 更好的IDE支持和自动补全
- ✅ 减少对 Hutool 的依赖
- ✅ 配置文件可读性提升

## 🔧 迁移步骤

### 1. 已完成的模块示例

#### mospital-allinpay
- ✅ **配置文件**: `allinpay.setting` → `allinpay.yaml`
- ✅ **数据类**: `AllinpayConfig.kt`
- ✅ **Setting类**: `AllinpaySetting.kt` 已更新

#### mospital-alipay  
- ✅ **配置文件**: `alipay.setting` → `alipay.yaml`
- ✅ **数据类**: `AlipayConfig.kt`（支持分组配置）
- ✅ **Setting类**: `AlipaySetting.kt` 已更新

### 2. 使用 ConfigGenerator 工具

运行生成器：
```kotlin
ConfigGenerator.generateConfigForModule(
    "donggang",
    "mospital-donggang/src/test/resources/einvoice.setting",
    "org.mospital.donggang"
)
```

### 3. 迁移模式示例

#### 简单配置（无分组）
```yaml
url: "https://example.com"
appId: "12345"
version: "1.0"
```

#### 分组配置
```yaml
database:
  host: "localhost"
  port: "3306"
redis:
  host: "127.0.0.1"
  port: "6379"
```

## 📝 待迁移模块

### 高优先级
- [ ] mospital-donggang (einvoice.setting)
- [ ] mospital-bsoft (bsoft.setting)
- [ ] mospital-zlsoft (zlsoft.setting)

### 中等优先级
- [ ] mospital-dongruan (his.setting)
- [ ] mospital-dongruan-pay (dongruanpay.setting)
- [ ] mospital-shine (shine.setting)

## 🛠️ 工具说明

### ConfigGenerator
- 位置: `mospital-common/src/main/kotlin/org/mospital/common/ConfigGenerator.kt`
- 功能: 自动生成配置类和YAML文件

### ConfigLoader
- 位置: `mospital-jackson/src/main/kotlin/org/mospital/jackson/ConfigLoader.kt`
- 功能: 统一的YAML配置文件加载器

## ✅ 迁移验证

- [ ] YAML配置文件格式正确
- [ ] 配置数据类字段完整
- [ ] Setting对象正确映射
- [ ] 功能测试通过 