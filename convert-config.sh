#!/bin/bash

# 转换配置文件从 .setting 到 .yaml 格式的脚本
# 使用方法: ./convert-config.sh

echo "🔄 开始转换配置文件格式..."

# 定义颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 查找所有的 .setting 文件
find . -name "*.setting" -type f | while read -r setting_file; do
    echo -e "${YELLOW}📋 处理文件: $setting_file${NC}"
    
    # 获取目录和文件名
    dir=$(dirname "$setting_file")
    filename=$(basename "$setting_file" .setting)
    yaml_file="$dir/$filename.yaml"
    
    # 如果 YAML 文件已存在，跳过
    if [[ -f "$yaml_file" ]]; then
        echo -e "${YELLOW}⚠️  YAML 文件已存在，跳过: $yaml_file${NC}"
        continue
    fi
    
    echo -e "${GREEN}✅ 转换: $setting_file -> $yaml_file${NC}"
    
    # 转换配置文件
    convert_setting_to_yaml "$setting_file" "$yaml_file"
done

echo -e "${GREEN}🎉 配置文件转换完成！${NC}"

# 转换函数
convert_setting_to_yaml() {
    local input_file="$1"
    local output_file="$2"
    
    # 创建临时文件
    local temp_file=$(mktemp)
    
    # 读取并转换文件
    {
        echo "# 从 $input_file 自动转换而来"
        echo "# 转换时间: $(date)"
        echo ""
        
        local current_section=""
        local section_indent=""
        
        while IFS= read -r line || [[ -n "$line" ]]; do
            # 跳过空行和注释
            if [[ -z "$line" || "$line" =~ ^[[:space:]]*# ]]; then
                if [[ -n "$line" && "$line" =~ ^[[:space:]]*# ]]; then
                    echo "$line"
                fi
                continue
            fi
            
            # 检查是否是分组标记 [section]
            if [[ "$line" =~ ^\[([^\]]+)\]$ ]]; then
                current_section="${BASH_REMATCH[1]}"
                echo ""
                echo "$current_section:"
                section_indent="  "
                continue
            fi
            
            # 处理键值对
            if [[ "$line" =~ ^[[:space:]]*([^=]+)=(.*)$ ]]; then
                local key="${BASH_REMATCH[1]}"
                local value="${BASH_REMATCH[2]}"
                
                # 清理键名（去除空格）
                key=$(echo "$key" | sed 's/[[:space:]]*$//')
                
                # 处理值（如果包含特殊字符则加引号）
                if [[ "$value" =~ [[:space:]]|[^[:alnum:]._/-] ]]; then
                    value="\"$value\""
                fi
                
                # 转换下划线键名为驼峰命名
                local yaml_key=$(convert_to_camel_case "$key")
                
                echo "${section_indent}${yaml_key}: $value"
            fi
        done < "$input_file"
    } > "$temp_file"
    
    # 移动临时文件到目标位置
    mv "$temp_file" "$output_file"
}

# 转换为驼峰命名的函数
convert_to_camel_case() {
    local input="$1"
    echo "$input" | sed -r 's/_([a-z])/\U\1/g'
} 