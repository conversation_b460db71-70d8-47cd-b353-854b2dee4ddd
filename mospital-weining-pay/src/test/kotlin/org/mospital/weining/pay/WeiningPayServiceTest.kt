package org.mospital.weining.pay

import kotlinx.coroutines.runBlocking
import org.dromara.hutool.core.util.RandomUtil
import org.junit.jupiter.api.Test
import org.mospital.jackson.DateTimeFormatters
import java.time.LocalDateTime
import java.time.ZoneId

class WeiningPayServiceTest {

    @Test
    fun testCreateZhuYuanOrder() {
        val outTradeNo = "ZY" + LocalDateTime.now(ZoneId.of("GMT+8")).format(DateTimeFormatters.PURE_DATETIME_FORMATTER) + RandomUtil.randomNumbers(4)
        val unifiedOrderForm = UnifiedOrderForm(
            businessType = BusinessType.ZHU_YUAN_CHONG_ZHI,
            goodsName = "住院充值",
            body = "住院充值",
            outTradeNo = outTradeNo,
            pageReturnUrl = "",
            tradeAmount = 1000,
            payParams = UnifiedOrderForm.PayParams(
                ip = "127.0.0.1",
                openId = "oQW3E5Vfwjs3lVM-5mtvolzPvXH0"
            )
        ).sign()
        val unifiedOrderResult = runBlocking {
            WeiningPayService.me.createUnifiedOrder(unifiedOrderForm)
        }
        assert(unifiedOrderResult.isOk()) { unifiedOrderResult.message }

        val orderInfo = unifiedOrderResult.data!!
        val queryOrderForm = QueryOrderForm(
            payOrderNo = orderInfo.payOrderNo
        ).sign()
        val queryOrderResult = runBlocking {
            WeiningPayService.me.queryOrder(queryOrderForm)
        }
        assert(queryOrderResult.isOk()) { queryOrderResult.message }
    }

    @Test
    fun testCreateMenZhenOrder() {
        val outTradeNo = "MZ" + LocalDateTime.now(ZoneId.of("GMT+8")).format(DateTimeFormatters.PURE_DATETIME_FORMATTER) + RandomUtil.randomNumbers(4)
        val unifiedOrderForm = UnifiedOrderForm(
            businessType = BusinessType.MEN_ZHEN_CHONG_ZHI,
            goodsName = "门诊充值",
            body = "门诊充值",
            outTradeNo = outTradeNo,
            pageReturnUrl = "",
            tradeAmount = 1000,
            payParams = UnifiedOrderForm.PayParams(
                ip = "127.0.0.1",
                openId = "oQW3E5Vfwjs3lVM-5mtvolzPvXH0"
            )
        ).sign()
        val unifiedOrderResult = runBlocking {
            WeiningPayService.me.createUnifiedOrder(unifiedOrderForm)
        }
        assert(unifiedOrderResult.isOk()) { unifiedOrderResult.message }

        val orderInfo = unifiedOrderResult.data!!
        val queryOrderForm = QueryOrderForm(
            payOrderNo = orderInfo.payOrderNo
        ).sign()
        val queryOrderResult = runBlocking {
            WeiningPayService.me.queryOrder(queryOrderForm)
        }
        assert(queryOrderResult.isOk()) { queryOrderResult.message }
    }

    @Test
    fun testQueryOrder() {
        val form = QueryOrderForm(
            payOrderNo = "I2024011120000001729452"
        ).sign()
        val result = runBlocking {
            WeiningPayService.me.queryOrder(form)
        }
        assert(result.isOk()) { result.message }
    }

    @Test
    fun testRefund() {
        val form = RefundForm(
            outRefundNo = "MZ202401112128461182",
            payOrderNo = "I2024011120000001729452",
            reason = "门诊预交金退款",
            refundAmount = 1000,
            tradeAmount = 1000
        ).sign()
        val result = runBlocking {
            WeiningPayService.me.refund(form)
        }
        assert(result.isOk()) { result.message }
    }

    @Test
    fun testQueryRefund() {
        val form = QueryRefundForm(
            payOrderNo = "I2024011121000001302651"
        ).sign()
        val result = runBlocking {
            WeiningPayService.me.queryRefund(form)
        }
        assert(result.isOk()) { result.message }
    }

}