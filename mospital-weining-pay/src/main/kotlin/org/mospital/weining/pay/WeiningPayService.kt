package org.mospital.weining.pay

import okhttp3.OkHttpClient
import org.mospital.common.http.HttpClientFactory
import org.mospital.jackson.JacksonKit
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import retrofit2.Retrofit
import retrofit2.converter.jackson.JacksonConverterFactory
import retrofit2.http.Body
import retrofit2.http.POST

interface WeiningPayService {

    companion object {
        private val logger: Logger = LoggerFactory.getLogger(WeiningPayService::class.java)
        private val serviceCache = mutableMapOf<String, WeiningPayService>()

        private fun createService(url: String): WeiningPayService {
            val httpClient: OkHttpClient = HttpClientFactory.createWithRequestIdLogging(
                logger = logger,
                connectTimeout = 2_000,
                readTimeout = 10_000,
                useUuidRequestId = true
            )
            val retrofit: Retrofit = Retrofit.Builder()
                .baseUrl(url)
                .client(httpClient)
                .addConverterFactory(JacksonConverterFactory.create(JacksonKit.buildMapper()))
                .build()
            return retrofit.create(WeiningPayService::class.java)
        }

        @Synchronized
        fun getService(url: String): WeiningPayService {
            return serviceCache.getOrPut(url) { createService(url) }
        }

        val me: WeiningPayService by lazy {
            getService(WeiningPaySetting.URL)
        }
    }

    @POST("blade-pay/pay/unifiedorder")
    suspend fun createUnifiedOrder(@Body form: UnifiedOrderForm): Result<UnifiedOrderResult>

    @POST("blade-pay/pay/queryOrder")
    suspend fun queryOrder(@Body form: QueryOrderForm): Result<QueryOrderResult>

    @POST("blade-pay/pay/refund")
    suspend fun refund(@Body form: RefundForm): Result<RefundResult>

    @POST("blade-pay/pay/queryRefundOrder")
    suspend fun queryRefund(@Body form: QueryRefundForm): Result<QueryRefundResult>

}