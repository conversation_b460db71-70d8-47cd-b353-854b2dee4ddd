package org.mospital.weining.pay

import com.fasterxml.jackson.annotation.JsonAlias

class QueryRefundResult {

    var thirdPartyNo: String = ""

    /**
     * 退款状态
     * create: 创建
     * no_refund: 未退款
     * have_refund: 已退款
     * closed: 已关闭
     * abnormal: 异常
     * refunding: 退款中
     * failure: 退款失败
     */
    var refundStatus: String = ""

    /**
     * 订单金额，单位为分
     */
    @JsonAlias("refundAmount", "refundFee")
    var refundAmount: Long = 0

    var outRefundNo: String = ""

    @JsonAlias("payOrderNo", "payOrderRefundNo")
    var payOrderRefundNo: String = ""

}