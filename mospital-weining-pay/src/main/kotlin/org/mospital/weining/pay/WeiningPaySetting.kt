package org.mospital.weining.pay

import org.mospital.jackson.ConfigLoader

object WeiningPaySetting {

    private val config: WeiningPayConfig by lazy {
        ConfigLoader.loadConfig("weining-pay.yaml", WeiningPayConfig::class.java, WeiningPaySetting::class.java)
    }

    val URL: String get() = config.url
    val MERCHANT_NO: String get() = config.merchantNo
    val MEN_ZHEN_CHANNEL_NO: String get() = config.menZhenChannelNo
    val ZHU_YUAN_CHANNEL_NO: String get() = config.zhuYuanChannelNo
    val MEN_ZHEN_SCENE_CODE: String get() = config.menZhenSceneCode
    val ZHU_YUAN_SCENE_CODE: String get() = config.zhuYuanSceneCode
    val KEY: String get() = config.key

}