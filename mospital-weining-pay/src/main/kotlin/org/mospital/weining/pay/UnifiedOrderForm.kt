package org.mospital.weining.pay

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonPropertyOrder
import org.mospital.common.IdUtil
import org.mospital.jackson.DateTimeFormatters
import org.mospital.jackson.JacksonKit
import java.time.LocalDateTime
import java.time.ZoneId

@Suppress("unused")
@JsonPropertyOrder(alphabetic = true)
class UnifiedOrderForm(@field:JsonIgnore val map: MutableMap<String, Any>) {

    val bank: String by map
    val merchantNo: String by map
    val businessType: String by map
    val closeTime: String by map
    val goodsName: String by map
    val body: String by map
    val nonceStr: String by map
    val outTradeNo: String by map
    val pageReturnUrl: String by map
    var payParams: PayParams by map
    val payScene: String by map
    val serverReturnUrl: String by map

    /**
     * 订单金额，单位为分
     */
    val tradeAmount: Long by map

    var sign: String = ""

    constructor(
        merchantNo: String = WeiningPaySetting.MERCHANT_NO,
        businessType: BusinessType,
        closeTime: String = LocalDateTime.now(ZoneId.of("GMT+8")).plusMinutes(10)
            .format(DateTimeFormatters.NORM_DATETIME_FORMATTER),
        goodsName: String,
        body: String,
        nonceStr: String = IdUtil.simpleUUID(),
        outTradeNo: String,
        pageReturnUrl: String,
        payParams: PayParams,
        serverReturnUrl: String = "",
        tradeAmount: Long
    ) : this(
        mutableMapOf(
            "bank" to businessType.getChannelNo(),
            "merchantNo" to merchantNo,
            "businessType" to businessType.code,
            "closeTime" to closeTime,
            "goodsName" to goodsName,
            "body" to body,
            "nonceStr" to nonceStr,
            "outTradeNo" to outTradeNo,
            "pageReturnUrl" to pageReturnUrl,
            "payParams" to payParams,
            "payScene" to businessType.getSceneCode(),
            "serverReturnUrl" to serverReturnUrl,
            "tradeAmount" to tradeAmount,
        )
    )

    @JsonPropertyOrder(alphabetic = true)
    data class PayParams(
        val appName: String = "",
        val authCode: String = "",
        val bundleId: String = "",
        val goodsId: String = "",
        val h5Type: String = "",
        val ip: String = "",
        val openId: String = "",
        val packageName: String = "",
        val subOpenId: String = "",
        val type: String = "",
        val wapName: String = "",
        val wapUrl: String = "",
    ) {
        override fun toString(): String = JacksonKit.writeValueAsString(this)
    }

    fun sign(): UnifiedOrderForm {
        this.sign = SecureUtil.sign(map)
        return this
    }
}