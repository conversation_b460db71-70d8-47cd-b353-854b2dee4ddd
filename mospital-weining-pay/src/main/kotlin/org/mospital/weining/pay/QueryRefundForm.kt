package org.mospital.weining.pay

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonPropertyOrder
import org.mospital.common.IdUtil

@JsonPropertyOrder(alphabetic = true)
class QueryRefundForm(@field:JsonIgnore val map: MutableMap<String, Any>) {

    val merchantNo: String by map
    val nonceStr: String by map

    /**
     * 退款返回的payOrderRefundNo
     */
    val payOrderNo: String by map

    var sign: String = ""

    constructor(
        merchantNo: String = WeiningPaySetting.MERCHANT_NO,
        nonceStr: String = IdUtil.simpleUUID(),
        payOrderNo: String
    ) : this(
        mutableMapOf(
            "merchantNo" to merchantNo,
            "nonceStr" to nonceStr,
            "payOrderNo" to payOrderNo
        )
    )

    fun sign(): QueryRefundForm {
        this.sign = SecureUtil.sign(map)
        return this
    }

}