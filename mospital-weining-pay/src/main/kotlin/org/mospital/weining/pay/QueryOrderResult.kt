package org.mospital.weining.pay

class QueryOrderResult {

    var thirdPartyNo: String? = ""

    var bankBillNo: String = ""

    /**
     * 交易状态
     * create: 创建
     * close: 关闭
     * success: 成功
     * failure: 失败
     * paying: 支付中
     * refunding: 退款
     */
    var tradeStatus: String = ""

    /**
     * 订单金额，单位为分
     */
    var tradeAmount: Long = 0

    var outTradeNo: String = ""

    var bank: String = ""

    var payOrderNo: String = ""

    /**
     * 实收金额，单位为分
     */
    var receiptAmount: Long = 0

}