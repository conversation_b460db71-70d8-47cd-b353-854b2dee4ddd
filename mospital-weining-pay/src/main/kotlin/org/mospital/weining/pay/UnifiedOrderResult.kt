package org.mospital.weining.pay

import com.fasterxml.jackson.annotation.JsonAlias

class UnifiedOrderResult {

    /**
     * 订单金额，单位为分
     */
    var tradeAmount: Long = 0

    var payOrderNo: String = ""

    var closeTime: String = ""

    var bank: String = ""

    var payType: String = ""

    var tenantName: String? = ""

    var tradeStatus: String? = ""

    var pageReturnUrl: String = ""

    var payResultParams: PayResultParams = PayResultParams()

    class PayResultParams {

        var timeStamp: String = ""

        @JsonAlias("package")
        var packageValue: String = ""

        var appId: String = ""

        @JsonAlias("sign", "paySign")
        var sign: String = ""

        var signType: String = ""

        var nonceStr: String = ""
    }

}