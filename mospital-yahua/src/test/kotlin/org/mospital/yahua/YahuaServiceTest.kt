package org.mospital.yahua

import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class YahuaServiceTest {

    @Test
    fun testGetReservationDoctorList() {
        val response: PatientQueueResponse = runBlocking {
            YahuaService.me.getPatientQueue(
                patientKey = "XN105575"
            )
        }
        assert(response.isOk()) { response.message ?: "" }
    }

}