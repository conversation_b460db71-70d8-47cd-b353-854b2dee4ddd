package org.mospital.yahua

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*

class YahuaSettingTest {
    
    @Test
    fun `should load configuration from YAML file`() {
        // 验证配置能正常加载
        assertNotNull(YahuaSetting.url)
        assertNotNull(YahuaSetting.hospitalId)
        
        // 验证配置值正确
        assertEquals("http://*************/", YahuaSetting.url)
        assertEquals("4e66fabafd944e458dc259ce890c31f3", YahuaSetting.hospitalId)
        
        // 验证配置不为空
        assertTrue(YahuaSetting.url.isNotBlank())
        assertTrue(YahuaSetting.hospitalId.isNotBlank())
    }
    
    @Test
    fun `should handle multiple access to configuration`() {
        // 验证多次访问配置（测试lazy加载）
        val url1 = YahuaSetting.url
        val url2 = YahuaSetting.url
        val hospitalId1 = YahuaSetting.hospitalId
        val hospitalId2 = YahuaSetting.hospitalId
        
        assertEquals(url1, url2)
        assertEquals(hospitalId1, hospitalId2)
    }
} 