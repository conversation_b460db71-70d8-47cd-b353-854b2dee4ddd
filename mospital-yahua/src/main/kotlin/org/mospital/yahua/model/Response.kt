package org.mospital.yahua.model

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 雅华API通用响应基类
 */
@JsonIgnoreProperties(ignoreUnknown = true)
open class Response<T> {
    @JsonProperty("status")
    val status: Int = -1
    
    @JsonProperty("desc")
    val message: String? = null
    
    @JsonProperty("data")
    open val data: T? = null

    fun isOk(): Boolean {
        return status == 0
    }

}