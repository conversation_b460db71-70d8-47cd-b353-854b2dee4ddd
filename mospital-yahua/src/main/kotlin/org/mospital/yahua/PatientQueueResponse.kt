package org.mospital.yahua

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import org.mospital.yahua.model.Response

/**
 * 患者队列响应实体类
 */
@JsonIgnoreProperties(ignoreUnknown = true)
class PatientQueueResponse : Response<List<PatientQueueItem>>() {
    @JsonProperty("data")
    override val data: List<PatientQueueItem> = emptyList()
}

/**
 * 患者队列项实体类
 */
@JsonIgnoreProperties(ignoreUnknown = true)
data class PatientQueueItem(
    @JsonProperty("registerId")
    val registerId: String? = null,
    
    @JsonProperty("patientCardKeyShow")
    val patientCardKeyShow: String? = null,
    
    @JsonProperty("patientCardKey")
    val patientCardKey: String? = null,
    
    @JsonProperty("queueName")
    val queueName: String? = null,
    
    @JsonProperty("deptId")
    val deptId: String? = null,

    /**
     * 科室名称
     */
    @JsonProperty("deptName")
    val deptName: String? = null,
    
    @JsonProperty("consultingName")
    val consultingName: String? = null,

    /**
     * 患者姓名
     */
    @JsonProperty("patientName")
    val patientName: String? = null,

    /**
     * 医生姓名
     */
    @JsonProperty("doctorName")
    val doctorName: String? = null,
    
    @JsonProperty("showName")
    val showName: String? = null,

    /**
     * 就诊时间
     */
    @JsonProperty("registerTime")
    val registerTime: String? = null,

    /**
     * 前方等待人数
     */
    @JsonProperty("waitNum")
    val waitNum: String? = null,
    
    @JsonProperty("registerobjKey")
    val registerobjKey: String? = null,

    /**
     * 队列名称
     */
    @JsonProperty("registerObjName")
    val registerObjName: String? = null,
    
    @JsonProperty("registerObjType")
    val registerObjType: String? = null,

    /**
     * 就诊状态：1=就诊中，2=过号，3=诊结
     */
    @JsonProperty("treatmentStatus")
    val treatmentStatus: String? = null,
    
    @JsonProperty("treatmentEndStatus")
    val treatmentEndStatus: String? = null,

    /**
     * 签到状态：0=未签到，1=已签到
     */
    @JsonProperty("signStatus")
    val signStatus: String? = null,
    
    @JsonProperty("queueId")
    val queueId: String? = null,
    
    @JsonProperty("patientKey")
    val patientKey: String? = null,
    
    @JsonProperty("isTriage")
    val isTriage: String? = null,
    
    @JsonProperty("isSign")
    val isSign: String? = null,
    
    @JsonProperty("bookSignInStatus")
    val bookSignInStatus: String? = null,
    
    @JsonProperty("systemId")
    val systemId: String? = null,
    
    @JsonProperty("id")
    val id: String? = null,

    /**
     * 挂号类型：1=普通号，2=专家号
     */
    @JsonProperty("relationType")
    val relationType: String? = null,
    
    @JsonProperty("doctorId")
    val doctorId: String? = null,
    
    @JsonProperty("doctorPicture")
    val doctorPicture: String? = null,
    
    @JsonProperty("doctorTitleName")
    val doctorTitleName: String? = null,
    
    @JsonProperty("doctorTitle")
    val doctorTitle: String? = null,
    
    @JsonProperty("sortLev")
    val sortLev: Int? = null,
    
    @JsonProperty("treatmentStage")
    val treatmentStage: String? = null
) 